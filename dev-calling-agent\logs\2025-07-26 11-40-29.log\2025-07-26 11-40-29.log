[ 2025-07-26 11:40:32,577 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:40:33,040 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4628 seconds.
[ 2025-07-26 11:40:36,913 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:40:45,973 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:40:45,975 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:40:52,783 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:40:52,784 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:40:53,611 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:40:53,615 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 11:40:53,629 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 11:40:53,629 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 11:40:53,629 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:40:53,674 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:40:54,013 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3387 seconds.
[ 2025-07-26 11:40:54,019 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:41:01,690 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:41:01,692 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:41:08,707 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:41:08,708 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:41:13,683 ] 69 root - ERROR - voice agent error: Deepgram API key is required
[ 2025-07-26 11:41:13,683 ] 19 livekit.agents - WARNING - Running <Task finished name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=NameError("name 'sys' is not defined")> took too long: 20.02 seconds
[ 2025-07-26 11:41:13,683 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 59, in entrypoint
    stt=deepgram.STT(model="nova-3", interim_results=True),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\deepgram\stt.py", line 137, in __init__
    raise ValueError("Deepgram API key is required")
ValueError: Deepgram API key is required

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 70, in entrypoint
    raise CustomException(e,sys)
                            ^^^
NameError: name 'sys' is not defined
[ 2025-07-26 11:41:35,829 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 11:41:35,832 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 11:41:35,834 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 11:41:35,835 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 11:41:35,838 ] 278 livekit.agents - DEBUG - job exiting
