[ 2025-07-26 15:26:58,930 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 15:26:59,315 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3854 seconds.
[ 2025-07-26 15:27:03,133 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:27:12,684 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 15:27:12,685 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:27:19,340 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 15:27:19,341 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 15:27:20,044 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:27:20,047 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 15:27:20,053 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 15:27:20,054 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 15:27:20,054 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:27:21,077 ] 475 livekit.agents - ERROR - failed to update the instructions
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 469, in _start_session
    update_instructions(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 767, in update_instructions
    llm.ChatMessage(id=INSTRUCTIONS_MESSAGE_ID, role="system", content=[instructions]),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for ChatMessage
content.0.ImageContent
  Input should be a valid dictionary or instance of ImageContent [type=model_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.AudioContent
  Input should be a valid dictionary or instance of AudioContent [type=model_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.str
  Input should be a valid string [type=string_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
[ 2025-07-26 15:27:21,086 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:27:21,087 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:27:21,087 ] 150 root - ERROR - voice agent error: sequence item 0: expected str instance, tuple found
[ 2025-07-26 15:27:21,087 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 147, in entrypoint
    await session.generate_reply(instructions="Hello! How may I assist you today?")
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_session.py", line 742, in generate_reply
    handle = self._activity._generate_reply(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 759, in _generate_reply
    instructions = "\n".join([self._agent.instructions, instructions])
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: sequence item 0: expected str instance, tuple found

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 151, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [147] error message [sequence item 0: expected str instance, tuple found]
[ 2025-07-26 15:27:28,445 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:27:29,519 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:27:29,520 ] 86 root - INFO - AgenticRAG tool invoked with query: 'Ahmedabad plane crash'
[ 2025-07-26 15:27:29,520 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:27:29,527 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:27:29,542 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-56a30001-3308-416c-98d2-f507c7c43cb6', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'Ahmedabad plane crash'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:27:29,543 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:27:30,245 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:57:30 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99927', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '43.8ms', 'x-request-id': 'req_01k132n2y1fdk975472fqd4x5h', 'via': '1.1 google', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=.b.kZRHwCrkoxXDNwgeTOv0qagiIXqKTRYipwTprDPs-**********-*******-hayC.rJWC1vvdYVGhi9OfzHnNC6Zk097eASDkf1_frgg4s8qt5I49z3WcArudAWxsL2O6VexOSOgXGEDNtmA_cR6JHBrsK8RQu9MHnKTosk; path=/; expires=Sat, 26-Jul-25 10:27:30 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9653087f5a853a35-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:27:30,260 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:27:30,261 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.7346 seconds.
[ 2025-07-26 15:27:30,264 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:27:30,264 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:27:30,264 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:27:31,444 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:27:31,445 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:27:31,446 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:27:31,447 ] 68 root - INFO - \u2705 Finished 'run' in 1.9268 seconds.
[ 2025-07-26 15:27:31,448 ] 103 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:27:31,453 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:27:32,099 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 15:28:31,095 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 15:28:31,098 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 15:28:31,105 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 15:28:31,106 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 15:28:31,107 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
