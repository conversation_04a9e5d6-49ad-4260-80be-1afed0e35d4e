[ 2025-07-26 15:16:38,449 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 15:16:38,846 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3978 seconds.
[ 2025-07-26 15:16:42,750 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:16:51,555 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 15:16:51,556 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:16:58,160 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 15:16:58,161 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 15:17:00,439 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:17:00,444 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 15:17:00,450 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 15:17:00,451 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 15:17:00,452 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:17:00,647 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 15:17:00,946 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.2985 seconds.
[ 2025-07-26 15:17:00,949 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:17:07,523 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 15:17:07,525 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:17:13,403 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 15:17:13,403 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 15:17:14,552 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 13.91 seconds
[ 2025-07-26 15:17:14,901 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:17:14,901 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:17:16,125 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 15:17:20,381 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:17:33,341 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:17:47,449 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:18:08,822 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:18:19,838 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:18:24,858 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:18:28,963 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:18:44,615 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:19:14,780 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:19:25,440 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:19:39,481 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 15:19:39,483 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 15:19:39,485 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 15:19:39,487 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 15:19:39,488 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
