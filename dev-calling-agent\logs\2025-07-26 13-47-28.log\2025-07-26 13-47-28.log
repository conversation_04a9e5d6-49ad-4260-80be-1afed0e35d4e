[ 2025-07-26 13:47:32,212 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 13:47:32,867 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.6551 seconds.
[ 2025-07-26 13:47:38,006 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 13:47:49,335 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 13:47:49,338 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 13:47:56,885 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 13:47:56,885 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 13:47:59,178 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 13:47:59,182 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 13:47:59,189 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 13:47:59,190 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 13:47:59,190 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 13:47:59,411 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 13:47:59,742 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3312 seconds.
[ 2025-07-26 13:47:59,745 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 13:48:07,430 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 13:48:07,432 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 13:48:16,342 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 13:48:16,344 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 13:48:17,759 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 18.34 seconds
[ 2025-07-26 13:48:18,419 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 13:48:18,420 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 13:48:20,118 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 13:48:25,457 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 13:48:54,635 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 13:49:18,387 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 13:49:40,712 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 13:50:01,244 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 13:50:01,247 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 13:50:01,254 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 13:50:01,254 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 13:50:01,255 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
