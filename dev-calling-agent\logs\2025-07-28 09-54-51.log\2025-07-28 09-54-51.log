[ 2025-07-28 09:54:54,764 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 09:54:55,185 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4214 seconds.
[ 2025-07-28 09:54:59,226 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:55:08,750 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:55:08,752 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:55:15,896 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:55:15,897 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 09:55:16,763 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:55:16,767 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 09:55:16,774 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 09:55:16,776 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:55:16,777 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 09:55:16,969 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:55:22,831 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:55:22,834 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:55:29,056 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:55:29,056 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 09:55:29,072 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 09:55:29,474 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 12.50 seconds
[ 2025-07-28 09:55:29,813 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:55:29,813 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:55:30,992 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 09:55:34,369 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:55:34,784 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:55:37,038 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:55:46,169 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 09:55:46,171 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 09:55:46,175 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 09:55:46,176 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 09:55:46,176 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
