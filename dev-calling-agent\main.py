from src.agent.agent import AgenticRAG
import time
def main():
    print("Hello from calling-agent!")
 


if __name__=="__main__":
    main()
    start = time.time()
    obj = AgenticRAG()
    obj.workflow
    loading_time = time.time() - start
    print()
    print(f"Server Loding time: {loading_time}")
    test_queries = [
        "What is the current chief justice of west bengal high court",
            # "What is the capital of France?",
            # "What is the temperature in New York today?"
            # "What are the different operational voltages of ABB switch gear and transformer cd dry type?",
            # "Compare and contrast RAG and Self-RAG approaches.", # Test missing doc handling
            # "What is the current temperature in Hyderabad?",
            # "Summarize the main points of the rag_arxiv paper." # Assuming 'rag_arxiv.pdf' is in 'data'
            # "What are the specifications of ABB JX245 and SMPGB joint?",
            # "What are the key components of a abb switchgear system?",
        ]

    print("\n" + "="*70)
    print("🧪 RUNNING TEST QUERIES")
    print("="*70)
    
    # Example question
    question = "What are the latest developments in machine learning?"
    question_2 = "What are the specifications of ABB JX245 and SMPGB joint?"

    # Run the workflow
    for i, q in enumerate(test_queries):
        start = time.time()
        print(f"\n📝 Test Query : {q}")
        print("-" * 50)
        response = obj.run(q)
        response_time = time.time() - start
        print(f"💬 Agent Response:\n{response}")
        print("\n" + "="*50)
        print("FINAL RESULT:")
        print("="*50)
        print(f"Question: {response['question']}")
        print(f"Answer: {response['generation']}")
        print(f"Single Document Response Time: {response_time}")
