[ 2025-07-24 16:22:44,209 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:22:56,039 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:22:56,045 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:23:05,563 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:23:05,564 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:23:07,596 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:23:07,658 ] 23 root - INFO - \U0001f50d Retrieving documents for: 'What are the different operational voltages of ABB switch gear and transformer cd dry type?...'
[ 2025-07-24 16:23:12,149 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:23:12,152 ] 109 root - INFO -  RAG Retrival Output: The ABB switchgear and transformer CD dry type has operational voltages of 145kV and 170kV.
[ 2025-07-24 16:23:12,152 ] 28 root - INFO - Rag Tool Response: The ABB switchgear and transformer CD dry type has operational voltages of 145kV and 170kV.
[ 2025-07-24 16:23:12,154 ] 29 root - INFO - Time Take: 4.495492935180664
[ 2025-07-24 16:23:12,157 ] 47 root - INFO - filter doc started
[ 2025-07-24 16:23:13,190 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:23:13,196 ] 54 root - INFO - ---------- Relevant Document -----------
[ 2025-07-24 16:23:13,197 ] 64 root - INFO -  Retriever Validation Compleed
[ 2025-07-24 16:23:13,197 ] 65 root - INFO - ============================ Retriever Validator Invoke Completed ============================== 
[ 2025-07-24 16:23:13,199 ] 20 root - INFO - =========================== GenerationTool Invoke ===========================
[ 2025-07-24 16:23:13,199 ] 24 root - INFO - Input document: [RetrieveDocument(binary_score='yes')]
[ 2025-07-24 16:23:13,199 ] 30 root - INFO - Reducing the context length
[ 2025-07-24 16:23:13,199 ] 51 root - ERROR - Exception in Generate :'list' object has no attribute 'page_content'
[ 2025-07-24 16:23:13,199 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\nodes\generation.py] line number [32] error message ['list' object has no attribute 'page_content']
