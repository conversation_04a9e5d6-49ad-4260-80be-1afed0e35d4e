[ 2025-07-24 16:26:53,492 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:27:05,629 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:27:05,635 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:27:13,414 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:27:13,414 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:27:15,250 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:27:15,286 ] 23 root - INFO - \U0001f50d Retrieving documents for: 'What are the different operational voltages of ABB switch gear and transformer cd dry type?...'
[ 2025-07-24 16:27:19,668 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:27:19,670 ] 109 root - INFO -  RAG Retrival Output: The ABB switchgear and transformer CD dry type has operational voltages of 145 kV and 170 kV.
[ 2025-07-24 16:27:19,671 ] 28 root - INFO - Rag Tool Response: The ABB switchgear and transformer CD dry type has operational voltages of 145 kV and 170 kV.
[ 2025-07-24 16:27:19,671 ] 29 root - INFO - Time Take: 4.384926795959473
[ 2025-07-24 16:27:19,674 ] 47 root - INFO - filter doc started
[ 2025-07-24 16:27:20,378 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:27:20,382 ] 54 root - INFO - ---------- Relevant Document -----------
[ 2025-07-24 16:27:20,383 ] 64 root - INFO -  Retriever Validation Compleed
[ 2025-07-24 16:27:20,383 ] 65 root - INFO - ============================ Retriever Validator Invoke Completed ============================== 
[ 2025-07-24 16:27:20,384 ] 20 root - INFO - =========================== GenerationTool Invoke ===========================
[ 2025-07-24 16:27:20,385 ] 25 root - INFO - type: [RetrieveDocument(binary_score='yes')]
[ 2025-07-24 16:27:20,385 ] 31 root - INFO - Reducing the context length
[ 2025-07-24 16:27:20,385 ] 52 root - ERROR - Exception in Generate :'list' object has no attribute 'page_content'
[ 2025-07-24 16:27:20,386 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\nodes\generation.py] line number [33] error message ['list' object has no attribute 'page_content']
