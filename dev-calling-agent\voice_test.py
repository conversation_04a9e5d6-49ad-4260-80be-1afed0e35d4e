from livekit.agents import (
    Agent,
    AgentSession,
    AutoSubscribe,
    JobContext,
    JobProcess,
    WorkerOptions,
    cli,
    metrics,
    RoomInputOptions,
    llm,
)
from livekit.plugins import (
    cartesia,
    #assemblyai,
    langchain,
    groq,
    deepgram,
    noise_cancellation,
    silero,
    speechify,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from src.agent.agent import AgenticRAG

# from adv_agentic_rag import query_info, simple_query_info, get_simple_llm_response
import logging
import asyncio
import re
# Enhanced Language Detection Imports
from langdetect import DetectorFactory
DetectorFactory.seed = 0

# Import multiple language detection libraries for better accuracy
try:
    from langdetect import detect, detect_langs, LangDetectError
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False
    print("Warning: langdetect not installed. Using fallback detection. Install with: pip install langdetect")

try:
    from polyglot.detect import Detector
    POLYGLOT_AVAILABLE = True
except ImportError:
    POLYGLOT_AVAILABLE = False
    print("Warning: polyglot not installed. Install with: pip install polyglot")

try:
    import pycld2 as cld2
    PYCLD2_AVAILABLE = True
except ImportError:
    PYCLD2_AVAILABLE = False
    print("Warning: pycld2 not installed. Install with: pip install pycld2")

try:
    import fasttext
    FASTTEXT_AVAILABLE = True
    # Download language identification model if not present
    try:
        fasttext_model = fasttext.load_model('lid.176.bin')
    except:
        fasttext_model = None
        print("Warning: FastText language model not found. Download with: wget https://dl.fbaipublicfiles.com/fasttext/supervised-models/lid.176.bin")
except ImportError:
    FASTTEXT_AVAILABLE = False
    fasttext_model = None
    print("Warning: fasttext not installed. Install with: pip install fasttext")


logger = logging.getLogger("voice-agent")
logger.setLevel(logging.DEBUG)


from dotenv import load_dotenv
import os
load_dotenv(dotenv_path=".env")
logger = logging.getLogger("voice-agent")

logger = logging.getLogger("voice-agent")
logger.setLevel(logging.DEBUG)


# Comprehensive language mapping for Indian languages
LANGUAGE_CODE_MAPPING = {
    # Primary Indian languages supported by langdetect
    'bn': 'bengali',      # Bengali
    'hi': 'hindi',        # Hindi
    'te': 'telugu',       # Telugu
    'ta': 'tamil',        # Tamil
    'mr': 'marathi',      # Marathi
    'gu': 'gujarati',     # Gujarati
    'kn': 'kannada',      # Kannada
    'ml': 'malayalam',    # Malayalam
    'pa': 'punjabi',      # Punjabi
    'or': 'odia',         # Odia
    'as': 'assamese',     # Assamese
    'ur': 'urdu',         # Urdu
    'ne': 'nepali',       # Nepali
    'si': 'sinhala',      # Sinhala
    'en': 'english',      # English
}

# Enhanced TTS language and voice mapping for Speechify
TTS_LANGUAGE_MAPPING = {
    'bengali': 'bn',
    'hindi': 'hi',
    'telugu': 'te',
    'tamil': 'ta',
    'marathi': 'mr',
    'gujarati': 'gu',
    'kannada': 'kn',
    'malayalam': 'ml',
    'punjabi': 'pa',
    'odia': 'or',
    'assamese': 'as',
    'urdu': 'ur',
    'nepali': 'ne',
    'sinhala': 'si',
    'english': 'en',
}

# Voice selection for each language (Speechify voice IDs)
VOICE_SELECTION = {
    'english': 'jack',  # Default English voice
    'hindi': 'simba-multilingual',  # Multilingual voice for Hindi
    'bengali': 'simba-multilingual',
    'tamil': 'simba-multilingual',
    'telugu': 'simba-multilingual',
    'marathi': 'simba-multilingual',
    'gujarati': 'simba-multilingual',
    'kannada': 'simba-multilingual',
    'malayalam': 'simba-multilingual',
    'punjabi': 'simba-multilingual',
    'urdu': 'simba-multilingual',
    'odia': 'simba-multilingual',
    'assamese': 'simba-multilingual',
    'nepali': 'simba-multilingual',
    'sinhala': 'simba-multilingual',
}

# STT language prompts for better recognition
STT_LANGUAGE_PROMPTS = {
    'english': "Provide accurate transcription of English speech.",
    'hindi': "Provide accurate transliteration of Hindi speech in English alphabet, maintaining the original language structure and pronunciation.",
    'bengali': "Provide accurate transliteration of Bengali speech in English alphabet, maintaining the original language structure and pronunciation.",
    'tamil': "Provide accurate transliteration of Tamil speech in English alphabet, maintaining the original language structure and pronunciation.",
    'telugu': "Provide accurate transliteration of Telugu speech in English alphabet, maintaining the original language structure and pronunciation.",
    'marathi': "Provide accurate transliteration of Marathi speech in English alphabet, maintaining the original language structure and pronunciation.",
    'gujarati': "Provide accurate transliteration of Gujarati speech in English alphabet, maintaining the original language structure and pronunciation.",
    'kannada': "Provide accurate transliteration of Kannada speech in English alphabet, maintaining the original language structure and pronunciation.",
    'malayalam': "Provide accurate transliteration of Malayalam speech in English alphabet, maintaining the original language structure and pronunciation.",
    'punjabi': "Provide accurate transliteration of Punjabi speech in English alphabet, maintaining the original language structure and pronunciation.",
    'urdu': "Provide accurate transliteration of Urdu speech in English alphabet, maintaining the original language structure and pronunciation.",
    'odia': "Provide accurate transliteration of Odia speech in English alphabet, maintaining the original language structure and pronunciation.",
    'assamese': "Provide accurate transliteration of Assamese speech in English alphabet, maintaining the original language structure and pronunciation.",
    'nepali': "Provide accurate transliteration of Nepali speech in English alphabet, maintaining the original language structure and pronunciation.",
    'sinhala': "Provide accurate transliteration of Sinhala speech in English alphabet, maintaining the original language structure and pronunciation.",
}

# Unicode ranges for Indian scripts (for fallback detection)
SCRIPT_RANGES = {
    'bengali': r'[\u0980-\u09FF]',      # Bengali
    'hindi': r'[\u0900-\u097F]',        # Devanagari (Hindi, Marathi, Nepali)
    'telugu': r'[\u0C00-\u0C7F]',       # Telugu
    'tamil': r'[\u0B80-\u0BFF]',        # Tamil
    'gujarati': r'[\u0A80-\u0AFF]',     # Gujarati
    'kannada': r'[\u0C80-\u0CFF]',      # Kannada
    'malayalam': r'[\u0D00-\u0D7F]',    # Malayalam
    'punjabi': r'[\u0A00-\u0A7F]',      # Gurmukhi (Punjabi)
    'odia': r'[\u0B00-\u0B7F]',         # Odia
    'assamese': r'[\u0980-\u09FF]',     # Assamese (same as Bengali)
    'urdu': r'[\u0600-\u06FF]',         # Arabic script (Urdu)
}

# Enhanced transliterated patterns for better voice recognition
TRANSLITERATED_PATTERNS = {
    'hindi': [
        # Basic words
        r'\b(kya|hai|aap|main|hum|tum|kaise|kahan|kab|kyun|namaste|dhanyawad|shukriya)\b',
        r'\b(haan|nahi|accha|theek|bilkul|zaroor|samjha|samjhi|bataiye|kahiye)\b',
        # Extended vocabulary
        r'\b(mera|tera|uska|humara|tumhara|unka|yeh|woh|yahan|wahan|abhi|phir)\b',
        r'\b(paani|khana|ghar|kaam|time|baat|log|dost|family|school|office)\b',
        r'\b(suniye|dekhiye|aiye|jaiye|kijiye|lijiye|dijiye|miliye|boliye)\b',
        r'\b(bharat|hindustan|desh|pradhanmantri|sarkar|rajya|sheher|gaon)\b',
        # Common phrases
        r'\b(kya haal|kaise ho|kya kar rahe|kahan ja rahe|kya chahiye|samay kya)\b'
    ],
    'bengali': [
        # Basic words
        r'\b(ki|ache|apni|ami|amra|tumi|kemn|kothay|kkhon|keno|namaskar|dhonnobad)\b',
        r'\b(haan|na|bhalo|thik|ekdom|nishchoi|bujhlam|bolen|bolo)\b',
        # Extended vocabulary
        r'\b(amar|tomar|tar|amader|tomader|tader|eta|ota|ekhane|okhane|ekhon|tarpor)\b',
        r'\b(jol|khabar|bari|kaj|somoy|kotha|manush|bondhu|poribar|school|office)\b',
        r'\b(shunun|dekhun|ashun|jan|korun|nin|din|boshun|bolun)\b',
        r'\b(bangla|bangladesh|desh|prodhanmontri|sarkar|rajjo|shohor|gram)\b',
        # Common phrases
        r'\b(ki obostha|kemon achen|ki korchen|kothay jachen|ki chai|somoy koto)\b'
    ],
    'tamil': [
        # Basic words
        r'\b(enna|irukku|neenga|naan|naanga|nee|epdi|enga|eppo|yen|vanakkam|nandri)\b',
        r'\b(aam|illa|nalla|sari|kandippa|sollunga|puriyuthu)\b',
        # Extended vocabulary
        r'\b(en|un|avan|ava|enga|unga|avanga|idhu|adhu|inga|anga|ippo|aprom)\b',
        r'\b(thanni|saapadu|veedu|velai|time|pesaradhu|aalunga|nanban|kudumbam)\b',
        r'\b(keelunga|paarunga|vaanga|ponga|pannunga|edunga|kudunga|ukkarunga)\b',
        r'\b(tamilnadu|india|naadu|mudalamaichar|arachu|maanilam|nagaram|ooru)\b',
        # Common phrases
        r'\b(enna vishayam|epdi irukeenga|enna panreenga|enga poreenga|enna venum|time enna)\b'
    ],
    'telugu': [
        # Basic words
        r'\b(enti|undi|meeru|nenu|manam|nuvvu|ela|ekkada|eppudu|enduku|namaskaram|dhanyavadalu)\b',
        r'\b(avunu|ledu|bagundi|sare|thappakunda|cheppandi|ardhamaindi)\b',
        # Extended vocabulary
        r'\b(naa|nee|atanu|aame|mana|mee|vaalla|idi|adi|ikkada|akkada|ippudu|taruvata)\b',
        r'\b(neellu|tindi|illu|pani|time|maata|manushulu|snehitudu|kutumbam)\b',
        r'\b(vinandi|chudandi|randi|vellandi|cheyandi|teesukondi|ivvandi|kurchondi)\b',
        r'\b(telangana|andhra|bharatam|mukhyamantri|prabhutvam|rajyam|nagaram|gramam)\b',
        # Common phrases
        r'\b(emi vishayam|ela unnaru|emi chestunnaru|ekkadiki veltunnaru|emi kavali|time enti)\b'
    ],
    'marathi': [
        # Basic words
        r'\b(kay|ahe|tumhi|mi|amhi|tu|kasa|kuthe|kevha|ka|namaskar|dhanyawad)\b',
        r'\b(ho|nahi|chan|thik|nakkichya|sanga|samajla)\b',
        # Extended vocabulary
        r'\b(maza|tuza|tyacha|amcha|tumcha|tyaancha|he|te|ikde|tikde|ata|mag)\b',
        r'\b(paani|jevan|ghar|kaam|vel|baat|lok|mitra|kutumb|shala|karyalay)\b',
        r'\b(eka|bagh|ya|ja|kar|ghe|de|bas|bol)\b',
        r'\b(maharashtra|bharat|desh|mukhyamantri|sarkar|rajya|shahar|gaav)\b',
        # Common phrases
        r'\b(kay haal|kasa ahes|kay kartos|kuthe jatos|kay pahije|vel kiti)\b'
    ],
    'gujarati': [
        # Basic words
        r'\b(shu|che|tame|hu|ame|tu|kem|kya|kyare|shu mate|namaste|abhar)\b',
        r'\b(haan|na|saras|thik|bilkul|kaho|samajyu)\b',
        # Extended vocabulary
        r'\b(maro|taro|eno|amaro|tamaro|emno|aa|te|ahi|tya|have|pachhi)\b',
        r'\b(paani|khavanu|ghar|kaam|time|vaat|manas|mitra|parivar|school|office)\b',
        r'\b(suno|juo|aavo|jao|karo|lo|apo|beso|bolo)\b',
        r'\b(gujarat|bharat|desh|mukhyamantri|sarkar|rajya|shaher|gaon)\b',
        # Common phrases
        r'\b(shu haal|kem cho|shu karo cho|kya jao cho|shu joiye|time shu)\b'
    ],
    'punjabi': [
        # Basic words
        r'\b(ki|hai|tusi|main|asi|tu|kive|kithe|kado|kyu|sat sri akal|dhanyawad)\b',
        r'\b(haan|nahi|changa|theek|bilkul|dasso|samjha|samjhi)\b',
        # Extended vocabulary
        r'\b(mera|tera|uska|sada|tusada|unka|eh|oh|ithe|othe|hun|phir)\b',
        r'\b(paani|khana|ghar|kaam|time|gall|lok|yaar|parivar|school|office)\b',
        r'\b(suno|dekho|aao|jao|karo|lao|dao|baitho|bolo)\b',
        r'\b(punjab|bharat|desh|mukhyamantri|sarkar|rajya|sheher|pind)\b'
    ],
    'urdu': [
        # Basic words
        r'\b(kya|hai|aap|main|hum|tum|kaise|kahan|kab|kyun|assalam|shukriya)\b',
        r'\b(haan|nahin|accha|theek|bilkul|zaroor|samjha|samjhi|batayiye|kahiye)\b',
        # Extended vocabulary
        r'\b(mera|tumhara|uska|hamara|aapka|unka|yeh|woh|yahan|wahan|abhi|phir)\b',
        r'\b(paani|khana|ghar|kaam|waqt|baat|log|dost|khandan|madrasa|daftar)\b',
        r'\b(suniye|dekhiye|aaiye|jaiye|kijiye|lijiye|dijiye|miliye|kahiye)\b'
    ]
}

def detect_original_language(text: str) -> str:
    """
    Enhanced multi-library language detection with confidence scoring:
    1. Multiple detection libraries (langdetect, polyglot, pycld2, fasttext)
    2. Transliterated pattern matching for Indian languages (highest priority for voice)
    3. Unicode script-based detection as fallback
    4. Confidence scoring and consensus-based decision making
    """
    if not text or len(text.strip()) < 2:
        return 'english'

    # Clean the text for better detection
    clean_text = text.strip().lower()
    original_text = text.strip()

    logger.debug(f"Detecting language for text: '{clean_text[:100]}...'")

    # Detection results storage
    detection_results = {}
    confidence_scores = {}

    # Method 1: Transliterated pattern matching (HIGHEST PRIORITY for voice input)
    # This is most important for Indian language voice input in English script
    pattern_matches = {}
    for language, patterns in TRANSLITERATED_PATTERNS.items():
        match_count = 0
        for pattern in patterns:
            matches = len(re.findall(pattern, clean_text, re.IGNORECASE))
            match_count += matches

        if match_count > 0:
            pattern_matches[language] = match_count
            logger.debug(f"Pattern matches for {language}: {match_count}")

    if pattern_matches:
        # Get language with most pattern matches
        best_pattern_lang = max(pattern_matches, key=pattern_matches.get)
        detection_results['pattern'] = best_pattern_lang
        confidence_scores['pattern'] = min(pattern_matches[best_pattern_lang] * 0.3, 1.0)  # High confidence for patterns
        logger.info(f"Language detected via transliterated patterns: {best_pattern_lang} (confidence: {confidence_scores['pattern']:.2f})")

    # Method 2: Multiple library detection
    library_results = []

    # Try langdetect with confidence
    if LANGDETECT_AVAILABLE:
        try:
            lang_probs = detect_langs(clean_text)
            if lang_probs:
                detected_lang_code = lang_probs[0].lang
                confidence = lang_probs[0].prob

                if detected_lang_code in LANGUAGE_CODE_MAPPING:
                    detected_language = LANGUAGE_CODE_MAPPING[detected_lang_code]
                    detection_results['langdetect'] = detected_language
                    confidence_scores['langdetect'] = confidence
                    library_results.append((detected_language, confidence))
                    logger.debug(f"Langdetect: {detected_language} (confidence: {confidence:.2f})")
        except (LangDetectError, Exception) as e:
            logger.debug(f"Langdetect failed: {e}")

    # Try polyglot
    if POLYGLOT_AVAILABLE:
        try:
            detector = Detector(original_text)
            if detector.language.code in LANGUAGE_CODE_MAPPING:
                detected_language = LANGUAGE_CODE_MAPPING[detector.language.code]
                confidence = detector.language.confidence / 100.0  # Convert to 0-1 scale
                detection_results['polyglot'] = detected_language
                confidence_scores['polyglot'] = confidence
                library_results.append((detected_language, confidence))
                logger.debug(f"Polyglot: {detected_language} (confidence: {confidence:.2f})")
        except Exception as e:
            logger.debug(f"Polyglot failed: {e}")

    # Try pycld2
    if PYCLD2_AVAILABLE:
        try:
            is_reliable, text_bytes_found, details = cld2.detect(original_text)
            if is_reliable and details:
                lang_code = details[0][1]
                confidence = details[0][2] / 100.0  # Convert to 0-1 scale

                # Map CLD2 codes to our language mapping
                cld2_to_our_mapping = {
                    'BENGALI': 'bengali', 'HINDI': 'hindi', 'TELUGU': 'telugu',
                    'TAMIL': 'tamil', 'MARATHI': 'marathi', 'GUJARATI': 'gujarati',
                    'KANNADA': 'kannada', 'MALAYALAM': 'malayalam', 'PUNJABI': 'punjabi',
                    'URDU': 'urdu', 'ENGLISH': 'english'
                }

                if lang_code in cld2_to_our_mapping:
                    detected_language = cld2_to_our_mapping[lang_code]
                    detection_results['pycld2'] = detected_language
                    confidence_scores['pycld2'] = confidence
                    library_results.append((detected_language, confidence))
                    logger.debug(f"PyCLD2: {detected_language} (confidence: {confidence:.2f})")
        except Exception as e:
            logger.debug(f"PyCLD2 failed: {e}")

    # Try FastText
    if FASTTEXT_AVAILABLE and fasttext_model:
        try:
            predictions = fasttext_model.predict(original_text.replace('\n', ' '), k=1)
            if predictions[0]:
                lang_code = predictions[0][0].replace('__label__', '')
                confidence = predictions[1][0]

                if lang_code in LANGUAGE_CODE_MAPPING:
                    detected_language = LANGUAGE_CODE_MAPPING[lang_code]
                    detection_results['fasttext'] = detected_language
                    confidence_scores['fasttext'] = confidence
                    library_results.append((detected_language, confidence))
                    logger.debug(f"FastText: {detected_language} (confidence: {confidence:.2f})")
        except Exception as e:
            logger.debug(f"FastText failed: {e}")

    # Method 3: Unicode script-based detection for native scripts
    script_scores = {}
    for language, pattern in SCRIPT_RANGES.items():
        char_count = len(re.findall(pattern, original_text))
        if char_count > 0:
            script_scores[language] = char_count

    if script_scores:
        detected_language = max(script_scores, key=script_scores.get)
        detection_results['unicode'] = detected_language
        confidence_scores['unicode'] = min(script_scores[detected_language] / len(original_text), 1.0)
        logger.debug(f"Unicode script: {detected_language} (confidence: {confidence_scores['unicode']:.2f})")

    # Decision making with weighted consensus
    if not detection_results:
        logger.debug("No language detected, defaulting to English")
        return 'english'

    # Priority-based decision making
    # 1. If pattern matching found something with high confidence, use it
    if 'pattern' in detection_results and confidence_scores['pattern'] > 0.6:
        final_language = detection_results['pattern']
        logger.info(f"Final language (pattern priority): {final_language}")
        return final_language

    # 2. If unicode script detection is confident, use it
    if 'unicode' in detection_results and confidence_scores['unicode'] > 0.3:
        final_language = detection_results['unicode']
        logger.info(f"Final language (unicode script): {final_language}")
        return final_language

    # 3. Use library consensus with highest confidence
    if library_results:
        # Sort by confidence and get the most confident result
        library_results.sort(key=lambda x: x[1], reverse=True)
        final_language = library_results[0][0]
        logger.info(f"Final language (library consensus): {final_language} (confidence: {library_results[0][1]:.2f})")
        return final_language

    # 4. Fallback to any detection result
    final_language = list(detection_results.values())[0]
    logger.info(f"Final language (fallback): {final_language}")
    return final_language

class VoiceAssistant(Agent):
    def __init__(self):
        # Initialize language tracking and TTS/STT management
        self.detected_language = None
        self.current_tts_language = 'english'
        self.current_stt_language = 'english'
        self.tool_usage_count = {"simple_llm_query": 0, "enhanced_query_info": 0, "direct_response": 0}

        # Store initial TTS and STT instances for dynamic switching
        self.base_tts = None
        self.base_stt = None
        rag_agent = AgenticRAG()
        super().__init__(
            instructions=(
                "You are a sophisticated, multilingual voice AI assistant with advanced language detection and consistency capabilities. Your primary directive is to maintain strict language consistency while providing accurate, contextual responses.\n\n"

                "**CRITICAL LANGUAGE RULES:**\n"
                "1. **Language Detection:** Advanced multi-library language detection is active with confidence scoring.\n"
                "2. **Response Language:** You MUST respond ONLY in the detected language for the ENTIRE conversation.\n"
                "3. **Transliteration Rule:** For Indian languages (Hindi, Bengali, Tamil, Telugu, etc.), "
                "respond using English alphabet transliteration of that language with proper pronunciation.\n"
                "4. **No Language Mixing:** Never mix English words unless they are proper nouns or technical terms "
                "without common translations.\n"
                "5. **Error Handling:** If any tool fails or you encounter errors, respond in the user's language. "
                "Never say 'I apologize' or use English phrases. Use appropriate phrases in the user's language.\n"
                "6. **Consistency:** Once a language is established, maintain it throughout the session with dynamic TTS/STT adjustment.\n\n"

                "**INTELLIGENT TOOL SELECTION & ROUTING:**\n"
                "- **Current Information:** Use web search for news, weather, recent events, real-time data\n"
                "- **Specific Domains:** Use RAG for Ahmedabad plane crash, electrical machines, technical specifications\n"
                "- **Simple Queries:** Answer directly for greetings, basic facts, simple calculations\n"
                "- **Efficiency:** Only use tools when absolutely necessary, prefer direct responses for simple queries\n\n"

                "**Enhanced Tool Usage Guidelines:**\n"
                "- Use `simple_query_tool` for: basic questions, greetings, simple facts, general knowledge, conversational responses\n"
                "- Use `enhanced_query_tool` ONLY for: current information needs, Ahmedabad plane crash queries, electrical machines queries, complex technical questions\n"
                "- Intelligent routing: Automatically detect query type and route to appropriate information source\n"
                "- Avoid unnecessary tool calling for simple conversational responses\n\n"

                "**Language-Specific Response Examples:**\n"
                "- Hindi: 'Aap kaise hain?' not 'How are you?' | 'Main aapki madad kar sakta hun'\n"
                "- Bengali: 'Apni kemon achen?' not 'How are you?' | 'Ami apnake sahajjo korte pari'\n"
                "- Tamil: 'Neenga eppadi irukeenga?' not 'How are you?' | 'Naan ungalukku uthavi seiya mudiyum'\n"
                "- Telugu: 'Meeru ela unnaru?' not 'How are you?' | 'Nenu mee sahayam cheyagalanu'\n"
                "- Error in Hindi: 'Mujhe kuch dikkat ho rahi hai, main is ke bare mein information dene ki koshish kar raha hun'\n\n"

                "Keep responses natural, conversational, and concise for voice interaction. Maintain original language pronunciation in transliteration."
            ),
            
            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
               
            ),
           llm=langchain.LLMAdapter(rag_agent.workflow),

            tts=speechify.TTS(
                model="simba-multilingual",
                
            ),
            turn_detection=MultilingualModel(),
            tools=[self.create_simple_query_tool(), self.create_enhanced_query_tool()],
        )

        # Store references for dynamic switching
        self.base_tts = self.tts
        self.base_stt = self.stt

    def update_tts_language(self, language: str) -> bool:
        """
        Dynamically update TTS language and voice based on detected language.
        Returns True if update was successful, False otherwise.
        """
        try:
            if language == self.current_tts_language:
                return True  # Already using correct language

            logger.info(f"Updating TTS language from {self.current_tts_language} to {language}")

            # Get appropriate voice for the language
            voice_id = VOICE_SELECTION.get(language, VOICE_SELECTION['english'])

            # Create new TTS instance with updated language settings
            new_tts = speechify.TTS(
                model="simba-multilingual",
                voice_id=voice_id,
            )

            # Update the agent's TTS
            self.tts = new_tts
            self.current_tts_language = language

            logger.info(f"TTS updated successfully to {language} with voice {voice_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to update TTS language to {language}: {e}")
            return False

    def update_stt_language(self, language: str) -> bool:
        """
        Dynamically update STT language and prompt based on detected language.
        Returns True if update was successful, False otherwise.
        """
        try:
            if language == self.current_stt_language:
                return True  # Already using correct language

            logger.info(f"Updating STT language from {self.current_stt_language} to {language}")

            # Get appropriate prompt for the language
            prompt = STT_LANGUAGE_PROMPTS.get(language, STT_LANGUAGE_PROMPTS['english'])

            # Create new STT instance with updated language settings
            new_stt = groq.STT(
                model="whisper-large-v3",
                language="en",  # Keep as 'en' for transliteration
                prompt=prompt,
            )

            # Update the agent's STT
            self.stt = new_stt
            self.current_stt_language = language

            logger.info(f"STT updated successfully to {language}")
            return True

        except Exception as e:
            logger.error(f"Failed to update STT language to {language}: {e}")
            return False

    def switch_language_models(self, language: str) -> bool:
        """
        Switch both TTS and STT to the specified language.
        Returns True if both updates were successful.
        """
        tts_success = self.update_tts_language(language)
        stt_success = self.update_stt_language(language)

        success = tts_success and stt_success
        if success:
            logger.info(f"Successfully switched language models to {language}")
        else:
            logger.warning(f"Partial failure switching to {language}: TTS={tts_success}, STT={stt_success}")

        return success

    def create_simple_query_tool(self):
        """Create simple query tool with enhanced intelligent response handling"""
        @llm.function_tool
        async def simple_query_tool(query: str) -> str:
            """
            Get responses for simple questions, greetings, basic facts, and general knowledge.
            Uses intelligent query classification for optimal routing.
            Automatically handles language detection and maintains consistency.
            """
            logger.info(f"Using simple_query_tool for: '{query}'")

            # Get the detected language from the assistant instance
            detected_lang = self.detected_language or detect_original_language(query)

            # Classify the query for intelligent routing
            classification = self.classify_query_type(query)
            logger.info(f"Query classification: {classification['type']} (confidence: {classification['confidence']:.2f}) - {classification['reasoning']}")

            # Handle based on classification
            if classification['type'] == 'conversational' and not classification['should_use_tools']:
                logger.info("Handling as direct conversational response")
                return await self._get_direct_response(query, detected_lang)

            # For domain-specific queries, redirect to enhanced tool
            if classification['type'] == 'domain_specific' and classification['recommended_tool'] == 'enhanced_query_tool':
                logger.info("Redirecting domain-specific query to enhanced tool")
                # This will be handled by the enhanced tool, but we'll process it here for now
                pass

            # Create language-aware prompt for the multi-tiered system
            if detected_lang != 'english':
                enhanced_query = f"[{detected_lang}] {query}"
            else:
                enhanced_query = query

            self.tool_usage_count["simple_llm_query"] += 1

            # Use the new simple_query_info function from the multi-tiered system
            try:
                return await simple_query_info(enhanced_query)
            except Exception as e:
                logger.error(f"Simple query tool failed: {e}")
                return await self._get_error_response(detected_lang, "information dene mein")

        return simple_query_tool

    def create_enhanced_query_tool(self):
        """Create enhanced query tool with intelligent routing and selective RAG usage"""
        @llm.function_tool
        async def enhanced_query_tool(query: str) -> str:
            """
            Enhanced tool with intelligent routing for:
            1. Current information needs (news, weather, recent events) -> Web Search
            2. Ahmedabad plane crash related queries -> RAG
            3. Electrical machines and equipment queries -> RAG
            4. Complex technical questions requiring research -> Multi-tiered approach

            Uses advanced query classification for optimal information source selection.
            """
            logger.info(f"Using enhanced_query_tool for: '{query}'")

            # Get the detected language from the assistant instance
            detected_lang = self.detected_language or detect_original_language(query)

            # Classify the query for intelligent routing
            classification = self.classify_query_type(query)
            logger.info(f"Enhanced tool classification: {classification['type']} (confidence: {classification['confidence']:.2f}) - {classification['reasoning']}")

            # Route based on classification with priority order

            # Priority 1: Domain-specific RAG queries
            if classification['type'] == 'domain_specific' or self._should_use_rag(query):
                logger.info("Routing to RAG retrieval for domain-specific query")
                # Create language-aware prompt for RAG system
                if detected_lang != 'english':
                    enhanced_query = f"[{detected_lang}] {query}"
                else:
                    enhanced_query = query

                self.tool_usage_count["enhanced_query_info"] += 1

                try:
                    return await query_info(enhanced_query)
                except Exception as e:
                    logger.error(f"RAG query failed: {e}")
                    return await self._get_error_response(detected_lang, "document search mein")

            # Priority 2: Current information queries
            elif classification['type'] == 'current_info' or self._needs_current_info(query):
                logger.info("Routing to web search for current information")
                try:
                    return await self._get_web_search_response(query, detected_lang)
                except Exception as e:
                    logger.error(f"Web search failed: {e}")
                    # Fallback to RAG if web search fails
                    logger.info("Web search failed, falling back to RAG")
                    try:
                        enhanced_query = f"[{detected_lang}] {query}" if detected_lang != 'english' else query
                        return await query_info(enhanced_query)
                    except Exception as e2:
                        logger.error(f"RAG fallback also failed: {e2}")
                        return await self._get_error_response(detected_lang, "web search aur document search mein")

            # Priority 3: Complex queries requiring research
            elif classification['type'] == 'complex':
                logger.info("Routing complex query to multi-tiered RAG system")
                if detected_lang != 'english':
                    enhanced_query = f"[{detected_lang}] {query}"
                else:
                    enhanced_query = query

                self.tool_usage_count["enhanced_query_info"] += 1

                try:
                    return await query_info(enhanced_query)
                except Exception as e:
                    logger.error(f"Complex query processing failed: {e}")
                    return await self._get_error_response(detected_lang, "complex query processing mein")

            # Fallback: Direct response for other queries
            else:
                logger.info("Falling back to direct response")
                return await self._get_direct_response(query, detected_lang)

        return enhanced_query_tool

    def _is_simple_conversational_query(self, query: str) -> bool:
        """Enhanced check for simple conversational responses that don't need tools"""
        simple_patterns = [
            # Greetings (multilingual)
            r'\b(hello|hi|hey|namaste|namaskar|vanakkam|adaab|sat sri akal|assalam)\b',
            r'\b(good morning|good evening|good night|subah bakhair|shubh ratri)\b',

            # How are you (multilingual)
            r'\b(how are you|kaise hain|kemon achen|eppadi irukeenga|ela unnaru|kasa ahes|kem cho)\b',
            r'\b(aap kaise|tumi kemon|neenga epdi|meeru ela|tumhi kase|tame kem)\b',

            # Thank you (multilingual)
            r'\b(thank you|thanks|dhanyawad|shukriya|nandri|dhanyavadalu|abhar)\b',

            # Basic questions
            r'\b(what is your name|aapka naam kya|tomar naam ki|ungal per enna|mee peru enti)\b',
            r'\b(who are you|aap kaun hain|tumi ke|neenga yaaru|meeru evaru)\b',
            r'\b(what can you do|aap kya kar sakte|tumi ki korte paro|neenga enna pannalam)\b',

            # Simple math
            r'^\d+\s*[\+\-\*\/]\s*\d+',
            r'\b(what is|kya hai|ki|enna|enti)\s+\d+\s*[\+\-\*\/]\s*\d+',

            # Time and date
            r'\b(what time|time kya|somoy koto|time enna|time enti)\b',
            r'\b(what date|date kya|tarikh koto|date enna|date enti)\b',

            # Simple facts
            r'\b(capital of|rajdhani|capital)\b',
            r'\b(who is|kaun hai|ke|yaaru|evaru)\s+(prime minister|pradhanmantri|mukhyamantri)\b',
        ]

        query_lower = query.lower()
        for pattern in simple_patterns:
            if re.search(pattern, query_lower):
                return True
        return False

    def _should_use_rag(self, query: str) -> bool:
        """Enhanced determination for RAG usage with specific domain detection"""
        rag_topics = [
            # Ahmedabad plane crash (multilingual)
            r'\b(ahmedabad|plane crash|flight crash|aircraft accident|viman durghatna|hawai jahaz)\b',
            r'\b(crash|accident|durghatna|hadsa|mishap)\s+(ahmedabad|gujarat)\b',
            r'\b(flight|viman|hawai jahaz|aircraft)\s+(crash|accident|durghatna)\b',

            # Electrical machines and equipment (multilingual)
            r'\b(electrical machine|motor|generator|transformer|switchgear|bijli ki machine)\b',
            r'\b(electric motor|bijli motor|vidyut motor|electrical generator)\b',
            r'\b(transformer|switchgear|control panel|electrical panel)\b',
            r'\b(abb|siemens|schneider|electrical equipment|bijli ka saman)\b',
            r'\b(voltage|current|power|electrical specification|bijli ki specification)\b',
            r'\b(induction motor|synchronous motor|dc motor|ac motor)\b',
            r'\b(electrical engineering|bijli engineering|vidyut engineering)\b',

            # Technical specifications
            r'\b(specification|spec|technical details|datasheet|manual)\b',
            r'\b(operating voltage|rated current|power rating|efficiency)\b',
            r'\b(installation|maintenance|troubleshooting|repair)\b',

            # Company/Brand specific
            r'\b(abb india|siemens india|schneider electric|l&t electrical)\b',
            r'\b(product catalog|price list|technical support)\b',
        ]

        query_lower = query.lower()
        for pattern in rag_topics:
            if re.search(pattern, query_lower):
                return True
        return False

    def _needs_current_info(self, query: str) -> bool:
        """Enhanced determination for current/real-time information needs"""
        current_info_patterns = [
            # Time indicators (multilingual)
            r'\b(today|now|current|latest|recent|this year|2024|2025)\b',
            r'\b(aaj|abhi|haal|taaza|naya|vartaman|hal)\b',  # Hindi
            r'\b(ajke|ekhon|notun|taaja|ekhoni|haal)\b',    # Bengali
            r'\b(inniki|ippo|puthu|latest|ippodhu|pudhu)\b',  # Tamil
            r'\b(eeroju|ippudu|kotha|taaja|vartamana)\b',   # Telugu
            r'\b(aaj|aata|nava|taaja|sadhya)\b',            # Marathi
            r'\b(aaje|have|navu|taaju|vartaman)\b',         # Gujarati

            # News and updates (multilingual)
            r'\b(news|breaking|update|happening|khabar|samachar)\b',
            r'\b(breaking news|taaja khabar|notun khobor|puthu seydhi)\b',
            r'\b(live news|live update|seedha prasaran)\b',

            # Weather (multilingual)
            r'\b(weather|temperature|forecast|mausam|tapman|hawa)\b',
            r'\b(rain|barish|brishti|mazha|varsha)\b',
            r'\b(climate|jalvayu|aabohawa)\b',

            # Financial/Market (multilingual)
            r'\b(stock price|market|exchange rate|share bazaar|stock market)\b',
            r'\b(sensex|nifty|bse|nse|share price)\b',
            r'\b(rupee|dollar|currency|mudra|paisa)\b',

            # Real-time indicators
            r'\b(live|real-time|right now|abhi abhi|turant|seedha)\b',
            r'\b(happening now|chal raha|ho raha|ghot raha)\b',

            # Sports and events
            r'\b(match|game|score|result|cricket|football|khel)\b',
            r'\b(live score|current score|abhi ka score)\b',

            # Traffic and travel
            r'\b(traffic|jam|route|road condition|sadak ki sthiti)\b',
            r'\b(train status|flight status|bus timing)\b',
        ]

        query_lower = query.lower()
        for pattern in current_info_patterns:
            if re.search(pattern, query_lower):
                return True
        return False

    def classify_query_type(self, query: str) -> dict:
        """
        Classify query type for intelligent routing with confidence scores.
        Returns a dictionary with classification results and confidence.
        """
        classification = {
            'type': 'unknown',
            'confidence': 0.0,
            'should_use_tools': True,
            'recommended_tool': None,
            'reasoning': ''
        }

        query_lower = query.lower()

        # Check for simple conversational queries (highest priority)
        if self._is_simple_conversational_query(query):
            classification.update({
                'type': 'conversational',
                'confidence': 0.9,
                'should_use_tools': False,
                'recommended_tool': 'direct_response',
                'reasoning': 'Simple greeting, basic question, or conversational response'
            })
            return classification

        # Check for RAG-specific topics
        if self._should_use_rag(query):
            classification.update({
                'type': 'domain_specific',
                'confidence': 0.85,
                'should_use_tools': True,
                'recommended_tool': 'enhanced_query_tool',
                'reasoning': 'Query matches specific RAG domains (Ahmedabad crash, electrical machines)'
            })
            return classification

        # Check for current information needs
        if self._needs_current_info(query):
            classification.update({
                'type': 'current_info',
                'confidence': 0.8,
                'should_use_tools': True,
                'recommended_tool': 'enhanced_query_tool',
                'reasoning': 'Query requires current/real-time information'
            })
            return classification

        # Check for general knowledge that might be answerable directly
        general_knowledge_patterns = [
            r'\b(what is|kya hai|ki|enna|enti)\s+[a-zA-Z]+\b',
            r'\b(who is|kaun hai|ke|yaaru|evaru)\s+[a-zA-Z\s]+\b',
            r'\b(where is|kahan hai|kothay|enga|ekkada)\s+[a-zA-Z\s]+\b',
            r'\b(when|kab|kkhon|eppozhu|eppudu)\b',
            r'\b(how|kaise|kemne|eppadi|ela)\b',
        ]

        for pattern in general_knowledge_patterns:
            if re.search(pattern, query_lower):
                classification.update({
                    'type': 'general_knowledge',
                    'confidence': 0.7,
                    'should_use_tools': True,
                    'recommended_tool': 'simple_query_tool',
                    'reasoning': 'General knowledge question that can be answered with simple LLM'
                })
                return classification

        # Default classification for complex queries
        classification.update({
            'type': 'complex',
            'confidence': 0.6,
            'should_use_tools': True,
            'recommended_tool': 'enhanced_query_tool',
            'reasoning': 'Complex query requiring research or analysis'
        })

        return classification

    async def _get_direct_response(self, query: str, language: str) -> str:
        """Get direct response without using external tools with language consistency"""
        try:
            response = await get_simple_llm_response(f"[{language}] {query}" if language != 'english' else query)

            # Validate and enforce language consistency
            validation = self.validate_response_language(response, language)
            if not validation['is_consistent']:
                logger.warning(f"Language consistency issues: {validation['issues']}")
                response = self.enforce_language_consistency(response, language)

            return response
        except Exception as e:
            logger.error(f"Direct response failed: {e}")
            return await self._get_error_response(language, "response generate karne mein")

    async def _get_web_search_response(self, query: str, language: str) -> str:
        """Get response using web search with language consistency enforcement"""
        try:
            # Import and use the web search functionality
            from adv_agentic_rag import initialize_rag_system, rag_system_instance

            # Initialize RAG system if needed
            if rag_system_instance is None:
                initialize_rag_system()

            # Use the web search tool directly
            web_response = rag_system_instance.tavily_search.search(query)

            # Format response in user's language with proper prefixes
            language_prefixes = {
                'hindi': "Web search ke anusaar:",
                'bengali': "Web search onujayi:",
                'tamil': "Web search padiye:",
                'telugu': "Web search prakaram:",
                'marathi': "Web search nusaar:",
                'gujarati': "Web search pramane:",
                'punjabi': "Web search anusar:",
                'urdu': "Web search ke mutabiq:",
                'english': "According to web search:"
            }

            prefix = language_prefixes.get(language, language_prefixes['english'])
            formatted_response = f"{prefix} {web_response}"

            # Enforce language consistency
            formatted_response = self.enforce_language_consistency(formatted_response, language)

            return formatted_response

        except Exception as e:
            logger.error(f"Web search failed: {e}")

            # Return error in user's language with consistency
            error_messages = {
                'hindi': f"Mujhe '{query}' ke bare mein current information dhundne mein dikkat ho rahi hai. Kripaya dobara koshish kariye.",
                'bengali': f"Ami '{query}' er bishoy e current information khujte somosya hocche. Doya kore abar chesta korun.",
                'tamil': f"Enakku '{query}' pathi current information thedura la problem irukku. Please try pannunga.",
                'telugu': f"Naaku '{query}' gurinchi current information kanukkovalani kashtam vastundi. Dayachesi malli try cheyandi.",
                'marathi': f"Mala '{query}' barobar current information shodnyat dikkat hot ahe. Kripaya punha prayatna kara.",
                'gujarati': f"Mane '{query}' vishay ma current information shodva ma dikkat che. Kripaya fari try karo.",
                'punjabi': f"Minu '{query}' bare current information labhna vich dikkat aa rahi hai. Kripaya dobara koshish karo.",
                'urdu': f"Mujhe '{query}' ke bare mein current information dhundne mein mushkil ho rahi hai. Meherbani kar ke dobara koshish kariye.",
                'english': f"I'm having trouble finding current information about '{query}'. Please try again."
            }

            error_response = error_messages.get(language, error_messages['english'])
            return self.enforce_language_consistency(error_response, language)

    def validate_response_language(self, response: str, expected_language: str) -> dict:
        """
        Validate if response maintains the expected language consistency.
        Returns validation results with suggestions for improvement.
        """
        validation = {
            'is_consistent': True,
            'confidence': 1.0,
            'issues': [],
            'suggestions': []
        }

        if not response or not expected_language:
            return validation

        # Detect language of the response
        response_language = detect_original_language(response)

        # Check if response language matches expected
        if response_language != expected_language:
            validation['is_consistent'] = False
            validation['confidence'] = 0.3
            validation['issues'].append(f"Response language ({response_language}) doesn't match expected ({expected_language})")
            validation['suggestions'].append(f"Rewrite response in {expected_language}")

        # Check for English words in non-English responses (except proper nouns)
        if expected_language != 'english':
            english_words = re.findall(r'\b[A-Za-z]+\b', response)
            # Filter out common technical terms and proper nouns that are acceptable
            acceptable_english = {
                'ok', 'okay', 'yes', 'no', 'please', 'thank', 'you', 'sir', 'madam',
                'india', 'bharat', 'delhi', 'mumbai', 'chennai', 'kolkata', 'bangalore',
                'pm', 'cm', 'president', 'minister', 'government', 'sarkar',
                'mobile', 'phone', 'computer', 'internet', 'email', 'whatsapp',
                'school', 'college', 'university', 'hospital', 'office',
                'rupee', 'dollar', 'bank', 'atm', 'card'
            }

            problematic_english = [word for word in english_words
                                 if word.lower() not in acceptable_english and len(word) > 2]

            if len(problematic_english) > len(english_words) * 0.3:  # More than 30% English
                validation['is_consistent'] = False
                validation['confidence'] = max(0.1, validation['confidence'] - 0.4)
                validation['issues'].append(f"Too many English words in {expected_language} response")
                validation['suggestions'].append(f"Replace English words with {expected_language} equivalents")

        return validation

    def enforce_language_consistency(self, response: str, expected_language: str) -> str:
        """
        Enforce language consistency by correcting common issues.
        Returns a corrected response maintaining the expected language.
        """
        if not response or expected_language == 'english':
            return response

        # Common English to Indian language replacements
        replacements = {
            'hindi': {
                r'\bI am\b': 'Main hun',
                r'\bI can\b': 'Main kar sakta hun',
                r'\bI will\b': 'Main karunga',
                r'\bI have\b': 'Mere paas hai',
                r'\bthank you\b': 'dhanyawad',
                r'\bsorry\b': 'maaf kijiye',
                r'\bplease\b': 'kripaya',
                r'\byes\b': 'haan',
                r'\bno\b': 'nahi',
                r'\bhelp\b': 'madad',
                r'\binformation\b': 'jaankari',
                r'\bproblem\b': 'samasya',
            },
            'bengali': {
                r'\bI am\b': 'Ami',
                r'\bI can\b': 'Ami pari',
                r'\bI will\b': 'Ami korbo',
                r'\bI have\b': 'Amar ache',
                r'\bthank you\b': 'dhonnobad',
                r'\bsorry\b': 'dukkhito',
                r'\bplease\b': 'doya kore',
                r'\byes\b': 'haan',
                r'\bno\b': 'na',
                r'\bhelp\b': 'sahajjo',
                r'\binformation\b': 'tothyo',
                r'\bproblem\b': 'somosya',
            },
            'tamil': {
                r'\bI am\b': 'Naan',
                r'\bI can\b': 'Ennal mudiyum',
                r'\bI will\b': 'Naan seiyen',
                r'\bI have\b': 'En kitta irukku',
                r'\bthank you\b': 'nandri',
                r'\bsorry\b': 'mannikkavum',
                r'\bplease\b': 'dayavu seydhu',
                r'\byes\b': 'aam',
                r'\bno\b': 'illa',
                r'\bhelp\b': 'uthavi',
                r'\binformation\b': 'thagaval',
                r'\bproblem\b': 'prachana',
            },
            'telugu': {
                r'\bI am\b': 'Nenu',
                r'\bI can\b': 'Nenu cheyagalanu',
                r'\bI will\b': 'Nenu chestanu',
                r'\bI have\b': 'Naa daggara undi',
                r'\bthank you\b': 'dhanyavadalu',
                r'\bsorry\b': 'kshaminchandi',
                r'\bplease\b': 'dayachesi',
                r'\byes\b': 'avunu',
                r'\bno\b': 'ledu',
                r'\bhelp\b': 'sahayam',
                r'\binformation\b': 'vivaraalu',
                r'\bproblem\b': 'samasya',
            }
        }

        if expected_language in replacements:
            corrected_response = response
            for english_pattern, local_replacement in replacements[expected_language].items():
                corrected_response = re.sub(english_pattern, local_replacement, corrected_response, flags=re.IGNORECASE)
            return corrected_response

        return response

    async def _get_error_response(self, language: str, context: str) -> str:
        """Get error response in user's language with consistency enforcement"""
        error_responses = {
            'hindi': f"Mujhe {context} kuch dikkat ho rahi hai. Main is ke bare mein information dene ki koshish kar raha hun.",
            'bengali': f"Amar {context} kichu somosya hocche. Ami ei bishoy e information deoar chesta korchi.",
            'tamil': f"Enakku {context} konjam problem aaguthu. Naan idhu pathi information kodukka try panren.",
            'telugu': f"Naaku {context} konni samasyalu vastunnayi. Nenu ee vishayam gurinchi information ivvadaniki try chestunnanu.",
            'marathi': f"Mala {context} thoda prashan ahe. Mi ya vishayavar mahiti denyacha prayatna karto ahe.",
            'gujarati': f"Mane {context} thodu problem che. Hu aa vishay ma information aapva try karu chu.",
            'punjabi': f"Minu {context} kuch dikkat aa rahi hai. Main is bare information dene di koshish kar raha han.",
            'urdu': f"Mujhe {context} kuch mushkil ho rahi hai. Main is ke bare mein maloomat dene ki koshish kar raha hun.",
            'english': f"I'm experiencing some difficulties with {context}. I'm trying to provide you with the information."
        }

        response = error_responses.get(language, error_responses['english'])

        # Enforce language consistency
        response = self.enforce_language_consistency(response, language)

        return response

    async def on_enter(self):
        logger.info("Voice assistant session started")
        logger.info(f"Available tools: {len(self.tools)} tools registered")
        
        # Send initial greeting in English (will be detected and switched if user responds in another language)
        await self.session.generate_reply(
            instructions="Greet the user and ask how you can help them today. Keep it brief and friendly.",
            allow_interruptions=True
        )

    async def on_user_speech_committed(self, message):
        """Enhanced speech processing with advanced language detection and dynamic TTS/STT switching"""
        user_query = message.content
        logger.info(f"User query received: '{user_query}'")

        # Detect language from user input using enhanced detection
        current_detected_lang = detect_original_language(user_query)

        # Set or maintain the detected language for the session
        if self.detected_language is None:
            self.detected_language = current_detected_lang
            logger.info(f"Language locked for session: {self.detected_language}")

            # Dynamically switch TTS and STT to detected language
            if self.detected_language != 'english':
                switch_success = self.switch_language_models(self.detected_language)
                if switch_success:
                    logger.info(f"Successfully switched TTS/STT to {self.detected_language}")
                else:
                    logger.warning(f"Failed to switch TTS/STT to {self.detected_language}, continuing with current settings")

        elif current_detected_lang != self.detected_language:
            # Log if language seems to have changed, but maintain consistency
            logger.info(f"Language change detected ({current_detected_lang}), but maintaining session language: {self.detected_language}")

            # Optionally, you could implement language switching mid-conversation here
            # For now, we maintain consistency as per requirements

        # Call parent method - language context is handled in the tools
        await super().on_user_speech_committed(message)

    def log_tool_usage(self, tool_name: str):
        """Log tool usage for debugging purposes."""
        self.tool_usage_count[tool_name] += 1
        logger.info(f"Tool usage stats: {self.tool_usage_count}")

    def log_language_consistency(self, query: str, response: str, expected_language: str):
        """Log language consistency metrics for monitoring and improvement"""
        try:
            # Validate response language consistency
            validation = self.validate_response_language(response, expected_language)

            # Log consistency metrics
            logger.info(f"Language Consistency Report:")
            logger.info(f"  Expected Language: {expected_language}")
            logger.info(f"  Query: '{query[:50]}...'")
            logger.info(f"  Response: '{response[:50]}...'")
            logger.info(f"  Is Consistent: {validation['is_consistent']}")
            logger.info(f"  Confidence: {validation['confidence']:.2f}")

            if validation['issues']:
                logger.warning(f"  Issues: {validation['issues']}")
            if validation['suggestions']:
                logger.info(f"  Suggestions: {validation['suggestions']}")

            # Track consistency over time (could be extended to store in database)
            consistency_score = validation['confidence']
            logger.info(f"  Session Language: {self.detected_language}")
            logger.info(f"  Current TTS Language: {self.current_tts_language}")
            logger.info(f"  Current STT Language: {self.current_stt_language}")
            logger.info(f"  Consistency Score: {consistency_score:.2f}")

        except Exception as e:
            logger.error(f"Error logging language consistency: {e}")

    async def generate_response_with_consistency_check(self, query: str, tool_response: str) -> str:
        """
        Generate final response with comprehensive language consistency checks.
        This method ensures the final response maintains language consistency.
        """
        try:
            expected_language = self.detected_language or 'english'

            # Enforce language consistency on the tool response
            consistent_response = self.enforce_language_consistency(tool_response, expected_language)

            # Validate the final response
            validation = self.validate_response_language(consistent_response, expected_language)

            # Log consistency metrics
            self.log_language_consistency(query, consistent_response, expected_language)

            # If still not consistent, apply additional corrections
            if not validation['is_consistent'] and validation['confidence'] < 0.7:
                logger.warning("Response still not consistent, applying additional corrections")

                # Additional correction logic for stubborn inconsistencies
                if expected_language != 'english':
                    # Add language-specific prefix to reinforce the language
                    language_reinforcement = {
                        'hindi': "Hindi mein jawab: ",
                        'bengali': "Bengali te uttor: ",
                        'tamil': "Tamil la badhil: ",
                        'telugu': "Telugu lo jawabu: ",
                        'marathi': "Marathi madhe uttar: ",
                        'gujarati': "Gujarati ma jawab: ",
                        'punjabi': "Punjabi vich jawab: ",
                        'urdu': "Urdu mein jawab: "
                    }

                    if expected_language in language_reinforcement:
                        # Only add prefix if response doesn't already start with language indicator
                        if not any(consistent_response.lower().startswith(prefix.lower())
                                 for prefix in language_reinforcement.values()):
                            consistent_response = language_reinforcement[expected_language] + consistent_response

            return consistent_response

        except Exception as e:
            logger.error(f"Error in consistency check: {e}")
            return tool_response  # Return original response if consistency check fails

def prewarm(proc):
    proc.userdata["vad"] = silero.VAD.load()

async def entrypoint(ctx: JobContext):
    logger.info(f"Connecting to room {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    participant = await ctx.wait_for_participant()
    logger.info(f"Starting voice assistant for participant {participant.identity}")

    session = AgentSession(
        vad=ctx.proc.userdata["vad"],
        min_endpointing_delay=0.5,
        max_endpointing_delay=60.0,
    )

    await session.start(
        room=ctx.room,
        agent=VoiceAssistant(),
        room_input_options=RoomInputOptions(
            noise_cancellation=noise_cancellation.BVC(),
        ),
    )

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
            drain_timeout=90,
        )
    )