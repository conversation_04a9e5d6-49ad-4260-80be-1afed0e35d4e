[ 2025-07-28 07:44:11,942 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 07:44:12,326 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3840 seconds.
[ 2025-07-28 07:44:16,749 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 07:44:28,019 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 07:44:28,022 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 07:44:37,543 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 07:44:37,544 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 07:44:38,860 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 07:44:38,864 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 07:44:38,876 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 07:44:38,877 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 07:44:38,878 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 07:44:39,087 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 07:44:47,783 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 07:44:47,787 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 07:44:56,998 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 07:44:56,999 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 07:44:57,020 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 07:44:57,502 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 18.42 seconds
[ 2025-07-28 07:44:57,885 ] 475 livekit.agents - ERROR - failed to update the instructions
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 469, in _start_session
    update_instructions(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 767, in update_instructions
    llm.ChatMessage(id=INSTRUCTIONS_MESSAGE_ID, role="system", content=[instructions]),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for ChatMessage
content.0.ImageContent
  Input should be a valid dictionary or instance of ImageContent [type=model_type, input_value=("You are a sophisticated...ral when spoken aloud",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.AudioContent
  Input should be a valid dictionary or instance of AudioContent [type=model_type, input_value=("You are a sophisticated...ral when spoken aloud",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.str
  Input should be a valid string [type=string_type, input_value=("You are a sophisticated...ral when spoken aloud",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
[ 2025-07-28 07:44:57,901 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 07:44:57,902 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 07:44:57,903 ] 314 root - ERROR - voice agent error: sequence item 0: expected str instance, tuple found
[ 2025-07-28 07:44:57,903 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 311, in entrypoint
    await session.generate_reply(instructions="Hello! How may I assist you today?")
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_session.py", line 742, in generate_reply
    handle = self._activity._generate_reply(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 759, in _generate_reply
    instructions = "\n".join([self._agent.instructions, instructions])
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: sequence item 0: expected str instance, tuple found

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 315, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [311] error message [sequence item 0: expected str instance, tuple found]
[ 2025-07-28 07:45:07,548 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:45:08,889 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 07:45:08,890 ] 199 root - INFO - Web search tool invoked with query: 'Kolkata weather forecast'
[ 2025-07-28 07:45:09,141 ] 168 root - INFO - Detected language: en for text: 'Kolkata weather forecast...'
[ 2025-07-28 07:45:09,141 ] 207 root - INFO - Language detected: English (en)
[ 2025-07-28 07:45:09,141 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 07:45:09,142 ] 45 root - INFO - \U0001f310 Web search for: 'Kolkata weather forecast...'
[ 2025-07-28 07:45:14,913 ] 57 root - INFO - Web search completed in 5.77s
[ 2025-07-28 07:45:14,914 ] 68 root - INFO - \u2705 Finished 'search_web' in 5.7725 seconds.
[ 2025-07-28 07:45:14,915 ] 213 root - INFO - Web search completed with 3 results
[ 2025-07-28 07:45:14,916 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-71' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 6.02 seconds
[ 2025-07-28 07:45:14,925 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 07:45:15,768 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 07:45:22,564 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 07:45:22,566 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 07:45:42,258 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:46:20,657 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:46:36,837 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:46:51,480 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:47:03,113 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:47:21,994 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:47:51,488 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:47:53,129 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:48:10,329 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:48:11,423 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 666, in _run
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\openai\_streaming.py", line 152, in __aiter__
    async for item in self._iterator:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\openai\_streaming.py", line 198, in __stream__
    raise APIError(
openai.APIError: tool call validation failed: attempted to call tool 'news_search' which was not request.tools

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 699, in _run
    raise APIConnectionError(retryable=retryable) from e
livekit.agents._exceptions.APIConnectionError: Connection error. (body=None, retryable=True)
[ 2025-07-28 07:48:12,898 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 07:48:12,899 ] 199 root - INFO - Web search tool invoked with query: 'Ahmedabad plane crash news'
[ 2025-07-28 07:48:12,899 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 07:48:12,900 ] 45 root - INFO - \U0001f310 Web search for: 'Ahmedabad plane crash news...'
[ 2025-07-28 07:48:18,219 ] 57 root - INFO - Web search completed in 5.32s
[ 2025-07-28 07:48:18,222 ] 68 root - INFO - \u2705 Finished 'search_web' in 5.3224 seconds.
[ 2025-07-28 07:48:18,223 ] 213 root - INFO - Web search completed with 3 results
[ 2025-07-28 07:48:18,223 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-445' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 5.33 seconds
[ 2025-07-28 07:48:18,232 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 07:48:54,156 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:49:10,131 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:49:56,314 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:49:57,423 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 07:49:57,424 ] 199 root - INFO - Web search tool invoked with query: 'ABB switchgear operating voltage'
[ 2025-07-28 07:49:57,424 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 07:49:57,425 ] 45 root - INFO - \U0001f310 Web search for: 'ABB switchgear operating voltage...'
[ 2025-07-28 07:50:03,481 ] 57 root - INFO - Web search completed in 6.06s
[ 2025-07-28 07:50:03,481 ] 68 root - INFO - \u2705 Finished 'search_web' in 6.0570 seconds.
[ 2025-07-28 07:50:03,481 ] 213 root - INFO - Web search completed with 3 results
[ 2025-07-28 07:50:03,483 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-599' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 6.06 seconds
[ 2025-07-28 07:50:03,493 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 07:50:53,761 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:51:07,481 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:51:09,056 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:51:10,514 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:51:12,552 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 07:51:38,093 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 07:51:38,095 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 07:51:38,101 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 07:51:38,101 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 07:51:38,103 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
