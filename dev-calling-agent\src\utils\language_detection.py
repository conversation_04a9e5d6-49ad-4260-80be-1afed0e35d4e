import sys
import time
from typing import Dict, Tuple, Optional
from functools import lru_cache
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException
from src.logging.logger import logging
from src.exception import CustomException

# Set seed for consistent results
DetectorFactory.seed = 0

# Pre-compiled keyword patterns for ultra-fast detection
FAST_LANGUAGE_PATTERNS = {
    'hi': ['namaste', 'kaise', 'kya', 'main', 'aap', 'hai', 'hoon', 'kar', 'kuch', 'mere'],
    'ta': ['vanakkam', 'eppadi', 'enna', 'naan', 'neenga', 'irukku', 'mudiyum', 'konjam', 'ungal'],
    'te': ['namaskaram', 'ela', 'enti', 'nenu', 'meeru', 'undi', 'cheyagalanu', 'konni', 'mee'],
    'de': ['hallo', 'wie', 'was', 'ich', 'sie', 'ist', 'kann', 'haben', 'werden'],
    'fr': ['bonjour', 'comment', 'que', 'je', 'vous', 'est', 'peut', 'avoir', 'être']
}

class LanguageDetector:
    """
    Language detection utility with transliteration support for Indian languages.
    Provides language detection and transliteration guidance for voice AI responses.
    """
    
    def __init__(self):
        # Common words/phrases for Indian language detection when transliterated
        self.indian_keywords = {
            'hi': ['namaste', 'aap', 'kaise', 'hain', 'main', 'mujhe', 'madad', 'chahiye', 'kar', 'sakta', 'hun', 'kya', 'hai', 'kahan', 'kab', 'kyun', 'kaise'],
            'ta': ['vanakkam', 'neenga', 'eppadi', 'irukeenga', 'naan', 'ungalukku', 'uthavi', 'seiya', 'mudiyum', 'enakku', 'venum', 'irukku', 'enna', 'enga', 'eppo'],
            'te': ['namaskaram', 'meeru', 'ela', 'unnaru', 'nenu', 'mee', 'sahayam', 'cheyagalanu', 'naaku', 'kavali', 'undi', 'emiti', 'ekkada', 'eppudu'],
            'bn': ['namaskar', 'ami', 'apni', 'kemon', 'achen', 'apnake', 'sahajjo', 'korte', 'pari', 'amar', 'lagbe', 'ache', 'ki', 'kothay', 'kobe'],
            'mr': ['namaskar', 'mi', 'tumhi', 'kase', 'ahat', 'tumhali', 'madad', 'karu', 'shakto', 'mala', 'pahije', 'ahe', 'kay', 'kuthe', 'kevha'],
            'gu': ['namaste', 'hun', 'tame', 'kem', 'cho', 'tamari', 'madad', 'kari', 'shaku', 'chu', 'mane', 'joiye', 'che', 'shu', 'kya', 'kyare'],
            'pa': ['sat', 'sri', 'akal', 'main', 'tusi', 'kiven', 'ho', 'tuhadi', 'madad', 'kar', 'sakda', 'han', 'manu', 'chahida', 'hai', 'ki', 'kithe', 'kado']
        }

        # Language mapping with transliteration patterns
        self.language_mapping = {
            'hi': {
                'name': 'Hindi',
                'code': 'hi',
                'transliteration_style': 'devanagari_roman',
                'sample_phrases': {
                    'greeting': 'Namaste, main aapki madad kar sakta hun',
                    'error': 'Mujhe kuch dikkat ho rahi hai',
                    'help': 'Main aapki sahayata kar sakta hun'
                }
            },
            'bn': {
                'name': 'Bengali',
                'code': 'bn',
                'transliteration_style': 'bengali_roman',
                'sample_phrases': {
                    'greeting': 'Namaskar, ami apnake sahajjo korte pari',
                    'error': 'Amar kichhu somosya hocche',
                    'help': 'Ami apnake sahajjo korte pari'
                }
            },
            'ta': {
                'name': 'Tamil',
                'code': 'ta',
                'transliteration_style': 'tamil_roman',
                'sample_phrases': {
                    'greeting': 'Vanakkam, naan ungalukku uthavi seiya mudiyum',
                    'error': 'Enakku konjam problem irukku',
                    'help': 'Naan ungalukku uthavi seiya mudiyum'
                }
            },
            'te': {
                'name': 'Telugu',
                'code': 'te',
                'transliteration_style': 'telugu_roman',
                'sample_phrases': {
                    'greeting': 'Namaskaram, nenu mee sahayam cheyagalanu',
                    'error': 'Naaku konni samasyalu unnaayi',
                    'help': 'Nenu mee sahayam cheyagalanu'
                }
            },
            'mr': {
                'name': 'Marathi',
                'code': 'mr',
                'transliteration_style': 'marathi_roman',
                'sample_phrases': {
                    'greeting': 'Namaskar, mi tumhali madad karu shakto',
                    'error': 'Mala kahi samasya ahe',
                    'help': 'Mi tumhali madad karu shakto'
                }
            },
            'gu': {
                'name': 'Gujarati',
                'code': 'gu',
                'transliteration_style': 'gujarati_roman',
                'sample_phrases': {
                    'greeting': 'Namaste, hun tamari madad kari shaku chu',
                    'error': 'Mane koi samasya che',
                    'help': 'Hun tamari madad kari shaku chu'
                }
            },
            'pa': {
                'name': 'Punjabi',
                'code': 'pa',
                'transliteration_style': 'punjabi_roman',
                'sample_phrases': {
                    'greeting': 'Sat sri akal, main tuhadi madad kar sakda han',
                    'error': 'Manu koi samasya hai',
                    'help': 'Main tuhadi madad kar sakda han'
                }
            },
            'en': {
                'name': 'English',
                'code': 'en',
                'transliteration_style': 'english',
                'sample_phrases': {
                    'greeting': 'Hello, I can help you',
                    'error': 'I am experiencing some issues',
                    'help': 'I can assist you'
                }
            },
            'de': {
                'name': 'German',
                'code': 'de',
                'transliteration_style': 'german_roman',
                'sample_phrases': {
                    'greeting': 'Hallo, ich kann Ihnen helfen',
                    'error': 'Ich habe einige Probleme',
                    'help': 'Ich kann Ihnen behilflich sein'
                }
            },
            'fr': {
                'name': 'French',
                'code': 'fr',
                'transliteration_style': 'french_roman',
                'sample_phrases': {
                    'greeting': 'Bonjour, je peux vous aider',
                    'error': 'Je rencontre quelques problemes',
                    'help': 'Je peux vous assister'
                }
            }
        }
        
        # Common Indian language codes that might be detected
        self.indian_languages = {'hi', 'bn', 'ta', 'te', 'mr', 'gu', 'pa', 'kn', 'ml', 'or', 'as'}

        # European languages that need transliteration
        self.european_languages = {'de', 'fr'}

        # All supported languages that need special handling
        self.supported_languages = self.indian_languages | self.european_languages | {'en'}
        
    @lru_cache(maxsize=200)
    def ultra_fast_detect(self, text_hash: str, text: str) -> Tuple[str, str, Dict]:
        """Ultra-fast cached language detection"""
        return self._internal_detect(text)

    def _internal_detect(self, text: str) -> Tuple[str, str, Dict]:
        """Internal detection logic"""
        try:
            if not text or len(text.strip()) < 3:
                return self._get_language_info('en')

            cleaned_text = text.strip().lower()

            # Ultra-fast pattern matching first
            for lang_code, patterns in FAST_LANGUAGE_PATTERNS.items():
                if any(pattern in cleaned_text for pattern in patterns[:3]):  # Check only first 3 patterns for speed
                    logging.info(f"⚡ Fast pattern match: {lang_code}")
                    return self._get_language_info(lang_code)

            # Fallback to keyword detection for Indian languages
            indian_lang = self._detect_indian_language_by_keywords(cleaned_text)
            if indian_lang:
                logging.info(f"⚡ Keyword detection: {indian_lang}")
                return self._get_language_info(indian_lang)

            # Quick langdetect for other languages
            detected_lang = detect(cleaned_text)
            logging.info(f"⚡ Langdetect: {detected_lang}")
            return self._get_language_info(detected_lang)

        except LangDetectException:
            return self._get_language_info('en')
        except Exception as e:
            logging.error(f"Detection error: {e}")
            return self._get_language_info('en')

    def detect_language(self, text: str) -> Tuple[str, str, Dict]:
        """
        Ultra-fast language detection with caching.

        Args:
            text: Input text to analyze

        Returns:
            Tuple of (language_code, language_name, language_config)
        """
        start_time = time.time()

        # Use hash-based caching for ultra-fast repeated detections
        text_hash = str(hash(text))
        result = self.ultra_fast_detect(text_hash, text)

        detection_time = time.time() - start_time
        if detection_time > 0.05:  # Log only if detection takes more than 50ms
            logging.info(f"⚡ Language detection: {detection_time:.3f}s")

        return result

    def _detect_indian_language_by_keywords(self, text: str) -> Optional[str]:
        """
        Detect Indian languages using keyword matching for transliterated text.

        Args:
            text: Cleaned, lowercase text

        Returns:
            Language code if detected, None otherwise
        """
        words = text.split()

        # Score each language based on keyword matches
        language_scores = {}

        for lang_code, keywords in self.indian_keywords.items():
            score = 0
            for word in words:
                if word in keywords:
                    score += 1

            if score > 0:
                language_scores[lang_code] = score

        # Return the language with the highest score if any matches found
        if language_scores:
            best_lang = max(language_scores, key=language_scores.get)
            # Require at least 1 keyword match for confidence
            if language_scores[best_lang] >= 1:
                return best_lang

        return None
    
    def _get_language_info(self, lang_code: str) -> Tuple[str, str, Dict]:
        """
        Get language information for a given language code.
        
        Args:
            lang_code: Language code (e.g., 'hi', 'en')
            
        Returns:
            Tuple of (language_code, language_name, language_config)
        """
        if lang_code in self.language_mapping:
            config = self.language_mapping[lang_code]
            return lang_code, config['name'], config
        else:
            # For unsupported languages, default to English
            logging.warning(f"Unsupported language code: {lang_code}. Defaulting to English.")
            config = self.language_mapping['en']
            return 'en', config['name'], config
    
    def get_transliteration_instructions(self, lang_code: str) -> str:
        """
        Get specific transliteration instructions for the detected language.

        Args:
            lang_code: Language code

        Returns:
            Detailed transliteration instructions for the LLM
        """
        if lang_code == 'en':
            return "Respond in clear, natural English."

        if lang_code in self.language_mapping:
            lang_info = self.language_mapping[lang_code]

            if lang_code in self.indian_languages:
                instructions = f"""
CRITICAL LANGUAGE INSTRUCTIONS for {lang_info['name']} ({lang_code}):

1. **MANDATORY TRANSLITERATION**: You MUST respond in {lang_info['name']} using English alphabet transliteration.
2. **PRONUNCIATION ACCURACY**: Use transliteration that matches natural {lang_info['name']} pronunciation patterns.
3. **NO ENGLISH MIXING**: Never mix English words. Use native {lang_info['name']} equivalents.
4. **NATURAL FLOW**: Make responses sound like a native {lang_info['name']} speaker, not machine-like.

EXAMPLE PHRASES for {lang_info['name']}:
- Greeting: "{lang_info['sample_phrases']['greeting']}"
- Error: "{lang_info['sample_phrases']['error']}"
- Help: "{lang_info['sample_phrases']['help']}"

TRANSLITERATION STYLE: {lang_info['transliteration_style']}
"""
            elif lang_code in self.european_languages:
                instructions = f"""
CRITICAL LANGUAGE INSTRUCTIONS for {lang_info['name']} ({lang_code}):

1. **LANGUAGE CONSISTENCY**: You MUST respond in {lang_info['name']} throughout the conversation.
2. **NATURAL PRONUNCIATION**: Use proper {lang_info['name']} words and sentence structure.
3. **NO ENGLISH MIXING**: Never mix English words unless absolutely necessary for technical terms.
4. **NATIVE SPEAKER QUALITY**: Make responses sound natural and fluent in {lang_info['name']}.

EXAMPLE PHRASES for {lang_info['name']}:
- Greeting: "{lang_info['sample_phrases']['greeting']}"
- Error: "{lang_info['sample_phrases']['error']}"
- Help: "{lang_info['sample_phrases']['help']}"

RESPONSE STYLE: Natural {lang_info['name']} with proper grammar and vocabulary
"""
            else:
                instructions = f"""
Respond in {lang_info['name']} language maintaining natural pronunciation patterns.
Avoid mixing with English and use native language equivalents.
"""

            return instructions
        else:
            # For unsupported languages, provide general guidance
            return f"""
Respond in {lang_code} language using appropriate transliteration if needed.
Maintain natural pronunciation patterns and avoid mixing with English.
"""
    
    def is_indian_language(self, lang_code: str) -> bool:
        """Check if the detected language is an Indian language."""
        return lang_code in self.indian_languages

    def is_european_language(self, lang_code: str) -> bool:
        """Check if the detected language is a European language."""
        return lang_code in self.european_languages

    def is_supported_language(self, lang_code: str) -> bool:
        """Check if the language is supported by the system."""
        return lang_code in self.supported_languages

    def get_language_type(self, lang_code: str) -> str:
        """Get the type of language for special handling."""
        if lang_code == 'en':
            return 'english'
        elif lang_code in self.indian_languages:
            return 'indian'
        elif lang_code in self.european_languages:
            return 'european'
        else:
            return 'other'

    def get_supported_languages(self) -> Dict[str, str]:
        """Get list of supported languages."""
        return {code: info['name'] for code, info in self.language_mapping.items()}

    def get_response_format_instructions(self, lang_code: str) -> str:
        """Get specific response format instructions for the language."""
        lang_type = self.get_language_type(lang_code)

        if lang_type == 'english':
            return "Respond in natural English with proper grammar and vocabulary."
        elif lang_type == 'indian':
            return f"Respond using English alphabet transliteration that sounds natural when spoken in {self.language_mapping[lang_code]['name']}."
        elif lang_type == 'european':
            return f"Respond in proper {self.language_mapping[lang_code]['name']} with correct grammar and native vocabulary."
        else:
            return "Respond in the detected language maintaining natural pronunciation patterns."
