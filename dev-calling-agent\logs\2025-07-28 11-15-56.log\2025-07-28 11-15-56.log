[ 2025-07-28 11:15:59,972 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 11:16:00,391 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4189 seconds.
[ 2025-07-28 11:16:04,282 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:16:13,144 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:16:13,147 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:16:19,345 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:16:19,346 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 11:16:20,121 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:16:20,124 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 11:16:20,129 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 11:16:20,132 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:16:20,132 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 11:16:20,325 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:16:26,292 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:16:26,295 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:16:31,890 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:16:31,890 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 11:16:31,914 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 11:16:32,324 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 12.02 seconds
[ 2025-07-28 11:16:32,741 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:16:32,742 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:16:33,791 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 11:16:38,691 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:16:53,144 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:16:53,791 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 11:16:53,792 ] 137 root - INFO - Web search: 'Who is the Prime Minister of India?'
[ 2025-07-28 11:16:53,792 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 11:16:53,793 ] 45 root - INFO - \U0001f310 Web search for: 'Who is the Prime Minister of India?...'
[ 2025-07-28 11:16:57,789 ] 57 root - INFO - Web search completed in 4.00s
[ 2025-07-28 11:16:57,790 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.9979 seconds.
[ 2025-07-28 11:16:57,790 ] 142 root - INFO - Web search completed with 3 results
[ 2025-07-28 11:16:57,791 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-128' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 3.98 seconds
[ 2025-07-28 11:16:57,795 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 11:17:07,483 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:17:08,240 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 11:17:08,241 ] 82 root - INFO - Vector database search: 'Hindi'
[ 2025-07-28 11:17:08,241 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-07-28 11:17:08,242 ] 37 root - INFO - \U0001f50d Vector DB search for: 'Hindi...'
[ 2025-07-28 11:17:09,866 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-34ff3282-628e-4ef4-863a-94dd0e6a6120', 'json_data': {'messages': [{'role': 'system', 'content': "You are a Question Answering assistant. Use the following pieces of retrieved context to answer the question. If you don't know the answer, say just 'I don't know' and Use the tool web_search if you don't know the answer or context is missing.. Use three sentences maximum and keep the answer concise.\n\nthe news or on social media; to impersonate others; or to automate the production of spam/phishing\ncontent [54]. Advanced language models may also lead to the automation of various jobs in the\ncoming decades [16]. In order to mitigate these risks, AI systems could be employed to \ufb01ght against\nmisleading content and automated spam/phishing.\nAcknowledgments\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this\npaper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\nwould also like to thank Kyunghyun Cho and Sewon Min for productive discussions and advice. EP\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\nprogram.\nReferences\n[1] Payal Bajaj, Daniel Campos, Nick Craswell, Li Deng, Jianfeng Gao, Xiaodong Liu, Rangan\nMajumder, Andrew McNamara, Bhaskar Mitra, Tri Nguyen, Mir Rosenberg, Xia Song, Alina\nStoica, Saurabh Tiwary, and Tong Wang. MS MARCO: A Human Generated MAchine\n\nthe news or on social media; to impersonate others; or to automate the production of spam/phishing\ncontent [54]. Advanced language models may also lead to the automation of various jobs in the\ncoming decades [16]. In order to mitigate these risks, AI systems could be employed to \ufb01ght against\nmisleading content and automated spam/phishing.\nAcknowledgments\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this\npaper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\nwould also like to thank Kyunghyun Cho and Sewon Min for productive discussions and advice. EP\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\nprogram.\nReferences\n[1] Payal Bajaj, Daniel Campos, Nick Craswell, Li Deng, Jianfeng Gao, Xiaodong Liu, Rangan\nMajumder, Andrew McNamara, Bhaskar Mitra, Tri Nguyen, Mir Rosenberg, Xia Song, Alina\nStoica, Saurabh Tiwary, and Tong Wang. MS MARCO: A Human Generated MAchine\n\nthe news or on social media; to impersonate others; or to automate the production of spam/phishing\ncontent [54]. Advanced language models may also lead to the automation of various jobs in the\ncoming decades [16]. In order to mitigate these risks, AI systems could be employed to \ufb01ght against\nmisleading content and automated spam/phishing.\nAcknowledgments\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this\npaper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\nwould also like to thank Kyunghyun Cho and Sewon Min for productive discussions and advice. EP\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\nprogram.\nReferences\n[1] Payal Bajaj, Daniel Campos, Nick Craswell, Li Deng, Jianfeng Gao, Xiaodong Liu, Rangan\nMajumder, Andrew McNamara, Bhaskar Mitra, Tri Nguyen, Mir Rosenberg, Xia Song, Alina\nStoica, Saurabh Tiwary, and Tong Wang. MS MARCO: A Human Generated MAchine\n\nthe news or on social media; to impersonate others; or to automate the production of spam/phishing\ncontent [54]. Advanced language models may also lead to the automation of various jobs in the\ncoming decades [16]. In order to mitigate these risks, AI systems could be employed to \ufb01ght against\nmisleading content and automated spam/phishing.\nAcknowledgments\nThe authors would like to thank the reviewers for their thoughtful and constructive feedback on this\npaper, as well as HuggingFace for their help in open-sourcing code to run RAG models. The authors\nwould also like to thank Kyunghyun Cho and Sewon Min for productive discussions and advice. EP\nthanks supports from the NSF Graduate Research Fellowship. PL is supported by the FAIR PhD\nprogram.\nReferences\n[1] Payal Bajaj, Daniel Campos, Nick Craswell, Li Deng, Jianfeng Gao, Xiaodong Liu, Rangan\nMajumder, Andrew McNamara, Bhaskar Mitra, Tri Nguyen, Mir Rosenberg, Xia Song, Alina\nStoica, Saurabh Tiwary, and Tong Wang. MS MARCO: A Human Generated MAchine\n\ncombine masked language models [8] with a differentiable retriever, have shown promising results,\narXiv:2005.11401v4  [cs.CL]  12 Apr 2021"}, {'role': 'user', 'content': 'Hindi'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-28 11:17:09,871 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-28 11:17:10,797 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Mon, 28 Jul 2025 05:47:13 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '98862', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '682.8ms', 'x-request-id': 'req_01k17s478vfw2rfpamzj27hg3g', 'via': '1.1 google', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=wkPck_aX.K6ZxhxKx_CWba8Qup0PlsMcnyVdHE4e5h8-1753681633-*******-9kmwUcKmEn2RRwp22mtwJ1IinH6UiMnej67bdX321ywqh_e_Vp2lhbVzhxgNtIVB7Tzxy3LUG2g0EMbdp3dILoI_KOPuNgkdZQSjqHIFiQk; path=/; expires=Mon, 28-Jul-25 06:17:13 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9662149b0bc03a37-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-28 11:17:10,817 ] 109 root - INFO -  RAG Retrival Output: I don't know
[ 2025-07-28 11:17:10,817 ] 51 root - INFO - Vector DB search completed in 2.57s
[ 2025-07-28 11:17:10,818 ] 52 root - INFO - Results relevant: False
[ 2025-07-28 11:17:10,818 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.5767 seconds.
[ 2025-07-28 11:17:10,818 ] 104 root - INFO - Vector database did not find relevant results
[ 2025-07-28 11:17:10,818 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-179' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 2.58 seconds
[ 2025-07-28 11:17:10,821 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 11:17:11,509 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 11:17:11,510 ] 137 root - INFO - Web search: 'What is Hindi'
[ 2025-07-28 11:17:11,510 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 11:17:11,510 ] 45 root - INFO - \U0001f310 Web search for: 'What is Hindi...'
[ 2025-07-28 11:17:15,930 ] 57 root - INFO - Web search completed in 4.42s
[ 2025-07-28 11:17:15,931 ] 68 root - INFO - \u2705 Finished 'search_web' in 4.4198 seconds.
[ 2025-07-28 11:17:15,931 ] 142 root - INFO - Web search completed with 3 results
[ 2025-07-28 11:17:15,932 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-201' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 4.42 seconds
[ 2025-07-28 11:17:15,937 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 11:17:24,594 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 11:17:24,596 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 11:17:24,599 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 11:17:24,601 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 11:17:24,602 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
