[ 2025-07-26 15:15:16,468 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 15:15:16,862 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3935 seconds.
[ 2025-07-26 15:15:20,686 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:15:29,319 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 15:15:29,320 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:15:36,625 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 15:15:36,625 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 15:15:37,485 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:15:37,489 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 15:15:37,496 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 15:15:37,496 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:15:37,498 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 15:15:38,422 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:15:38,423 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:15:39,694 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 15:15:45,356 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:15:46,156 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:15:46,157 ] 103 root - INFO - AgenticRAG tool invoked with query: 'What is the name of the postman's face?'
[ 2025-07-26 15:15:46,157 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:15:46,160 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:15:46,168 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-40655259-7e03-4967-bf0c-7f9c16927598', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': "What is the name of the postman's face?"}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:15:46,168 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:15:46,772 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:45:47 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99923', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '46.2ms', 'x-request-id': 'req_01k131zm0mf8ja5kmx27evw55j', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=0EX7LwecR3uLUGVOQzCd9htsyc0U8cKwz7rWuKxnRjo-**********-*******-8X2jZYA6V1yX_g7ydMARcuPI1.s86c7KunyjDCMrIpl3M5pSUnebas87Zdx3VqvpLlixFXc.1wG22WpLHfW6kzx0mvV4rFK1Uw6PgXoKk8I; path=/; expires=Sat, 26-Jul-25 10:15:47 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9652f7531e933e46-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:15:46,785 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:15:46,786 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.6257 seconds.
[ 2025-07-26 15:15:46,788 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:15:46,789 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:15:46,790 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:15:47,820 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:15:47,821 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:15:47,821 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:15:47,821 ] 68 root - INFO - \u2705 Finished 'run' in 1.6638 seconds.
[ 2025-07-26 15:15:47,821 ] 120 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:15:47,824 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:16:02,334 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:16:13,683 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:16:17,257 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:16:18,868 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 15:16:18,870 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 15:16:18,872 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 15:16:18,874 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 15:16:18,874 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
