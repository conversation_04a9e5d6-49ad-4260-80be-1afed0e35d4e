[ 2025-07-26 15:02:15,979 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 15:02:16,419 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4399 seconds.
[ 2025-07-26 15:02:20,727 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:02:30,630 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 15:02:30,632 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:02:37,419 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 15:02:37,419 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 15:02:38,479 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:02:38,484 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 15:02:38,495 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 15:02:38,497 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 15:02:38,497 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:02:39,955 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:02:39,956 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:02:41,324 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 15:02:45,957 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:02:56,671 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:03:16,314 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:03:37,835 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 15:03:37,837 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 15:03:37,839 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 15:03:37,842 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 15:03:37,843 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
