[ 2025-07-28 11:15:03,141 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 11:15:03,559 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4177 seconds.
[ 2025-07-28 11:15:07,607 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:15:15,601 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:15:15,603 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:15:21,682 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:15:21,684 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 11:15:22,447 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:15:22,452 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 11:15:22,458 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 11:15:22,460 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:15:22,461 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 11:15:22,653 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:15:28,294 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:15:28,298 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:15:34,508 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:15:34,509 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 11:15:34,533 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 11:15:34,944 ] 271 root - ERROR - Voice agent error: TTS.__init__() got an unexpected keyword argument 'voice'
[ 2025-07-28 11:15:34,944 ] 19 livekit.agents - WARNING - Running <Task finished name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=CustomException(TypeError("TTS.__init__() got an unexpected keyword argument 'voice'"))> took too long: 12.30 seconds
[ 2025-07-28 11:15:34,945 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 250, in entrypoint
    tts=speechify.TTS(
        ^^^^^^^^^^^^^^
TypeError: TTS.__init__() got an unexpected keyword argument 'voice'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 272, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [250] error message [TTS.__init__() got an unexpected keyword argument 'voice']
[ 2025-07-28 11:15:51,688 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 11:15:51,690 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 11:15:51,692 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-28 11:15:51,694 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 11:15:51,696 ] 278 livekit.agents - DEBUG - job exiting
