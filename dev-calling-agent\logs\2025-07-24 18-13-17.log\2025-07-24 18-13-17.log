[ 2025-07-24 18:13:33,731 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 18:13:44,333 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 18:13:44,337 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 18:13:51,891 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 18:13:51,892 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 18:13:55,594 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 18:13:55,624 ] 66 root - ERROR - Error Route: 'AIMessage' object has no attribute 'lower'
[ 2025-07-24 18:13:55,624 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\route.py] line number [55] error message ['AIMessage' object has no attribute 'lower']
