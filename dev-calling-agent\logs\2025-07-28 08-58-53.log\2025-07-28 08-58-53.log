[ 2025-07-28 08:59:03,495 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 08:59:03,911 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4166 seconds.
[ 2025-07-28 08:59:40,176 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 08:59:50,896 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 08:59:50,897 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 08:59:57,632 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 08:59:57,633 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 08:59:58,717 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 08:59:58,724 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 08:59:58,737 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 08:59:58,740 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 08:59:58,740 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 08:59:58,925 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:00:06,269 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:00:06,271 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:00:12,681 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:00:12,682 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 09:00:12,720 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 09:00:13,224 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 14.30 seconds
[ 2025-07-28 09:00:14,010 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:00:14,011 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:00:15,438 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 09:00:18,827 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:00:39,875 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:00:40,897 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:00:40,899 ] 261 root - INFO - Web search tool invoked with query: 'current prime minister of India'
[ 2025-07-28 09:00:41,629 ] 168 root - INFO - Detected language: en for text: 'current prime minister of India...'
[ 2025-07-28 09:00:41,630 ] 269 root - INFO - Language detected: English (en)
[ 2025-07-28 09:00:41,630 ] 303 root - ERROR - Web search tool error: property 'instructions' of 'LanguageAwareAgent' object has no setter
[ 2025-07-28 09:00:41,631 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:00:42,453 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:00:42,454 ] 261 root - INFO - Web search tool invoked with query: 'who is the current prime minister of India'
[ 2025-07-28 09:00:42,454 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 09:00:42,455 ] 45 root - INFO - \U0001f310 Web search for: 'who is the current prime minister of India...'
[ 2025-07-28 09:00:49,212 ] 57 root - INFO - Web search completed in 6.76s
[ 2025-07-28 09:00:49,214 ] 68 root - INFO - \u2705 Finished 'search_web' in 6.7594 seconds.
[ 2025-07-28 09:00:49,214 ] 279 root - INFO - Web search completed with 3 results
[ 2025-07-28 09:00:49,215 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-160' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 6.77 seconds
[ 2025-07-28 09:00:49,224 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:01:07,045 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 09:01:07,046 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 09:01:07,054 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 09:01:07,055 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 09:01:07,057 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
