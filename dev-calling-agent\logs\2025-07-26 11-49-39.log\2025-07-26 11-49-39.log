[ 2025-07-26 11:49:42,505 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:49:42,974 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4688 seconds.
[ 2025-07-26 11:49:47,312 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:49:57,263 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:49:57,266 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:50:04,205 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:50:04,206 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:50:05,498 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:50:05,504 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 11:50:05,523 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 11:50:05,525 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:50:05,526 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 11:50:05,564 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:50:05,892 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3284 seconds.
[ 2025-07-26 11:50:05,894 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:50:13,441 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:50:13,443 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:50:20,187 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:50:20,188 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:50:21,211 ] 76 root - ERROR - voice agent error: name 'groq' is not defined
[ 2025-07-26 11:50:21,211 ] 19 livekit.agents - WARNING - Running <Task finished name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=CustomException(NameError("name 'groq' is not defined"))> took too long: 15.64 seconds
[ 2025-07-26 11:50:21,212 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 60, in entrypoint
    stt=groq.STT(
        ^^^^
NameError: name 'groq' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 77, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py] line number [60] error message [name 'groq' is not defined]
[ 2025-07-26 11:51:01,057 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 11:51:01,061 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 11:51:01,064 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 11:51:01,065 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 11:51:01,066 ] 278 livekit.agents - DEBUG - job exiting
