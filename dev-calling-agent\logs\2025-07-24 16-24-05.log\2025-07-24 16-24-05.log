[ 2025-07-24 16:24:27,776 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:24:39,518 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:24:39,523 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:24:49,762 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:24:49,762 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:24:53,631 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:24:53,679 ] 23 root - INFO - \U0001f50d Retrieving documents for: 'What are the different operational voltages of ABB switch gear and transformer cd dry type?...'
[ 2025-07-24 16:24:57,835 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:24:57,838 ] 109 root - INFO -  RAG Retrival Output: According to the provided context, the operational voltage of ABB Switchgear and Transformer CD Dry Type is 145/170 kV.
[ 2025-07-24 16:24:57,838 ] 28 root - INFO - Rag Tool Response: According to the provided context, the operational voltage of ABB Switchgear and Transformer CD Dry Type is 145/170 kV.
[ 2025-07-24 16:24:57,838 ] 29 root - INFO - Time Take: 4.159139394760132
[ 2025-07-24 16:24:57,840 ] 47 root - INFO - filter doc started
[ 2025-07-24 16:24:58,586 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:24:58,589 ] 54 root - INFO - ---------- Relevant Document -----------
[ 2025-07-24 16:24:58,589 ] 64 root - INFO -  Retriever Validation Compleed
[ 2025-07-24 16:24:58,589 ] 65 root - INFO - ============================ Retriever Validator Invoke Completed ============================== 
[ 2025-07-24 16:24:58,591 ] 20 root - INFO - =========================== GenerationTool Invoke ===========================
[ 2025-07-24 16:24:58,591 ] 122 root - INFO - Agent Weorflow Error: 'list' object has no attribute 'page_content'
