[ 2025-07-28 12:19:59,698 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 12:20:00,121 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4233 seconds.
[ 2025-07-28 12:20:04,025 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:20:12,820 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:20:12,821 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:20:19,656 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:20:19,657 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 12:20:20,378 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:20:20,381 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 12:20:20,381 ] 385 livekit.agents - INFO - starting inference executor
[ 2025-07-28 12:20:20,400 ] 167 livekit.agents - INFO - initializing process
[ 2025-07-28 12:20:30,405 ] 248 livekit.agents - INFO - killing process
[ 2025-07-28 12:20:30,406 ] 81 livekit.agents - ERROR - worker failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\asyncio\tasks.py", line 500, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\channel.py", line 47, in arecv_message
    return _read_message(await dplx.recv_bytes(), messages)
                         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\aio\duplex_unix.py", line 35, in recv_bytes
    len_bytes = await self._reader.readexactly(4)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\asyncio\streams.py", line 750, in readexactly
    await self._wait_for_data('readexactly')
  File "D:\conda\envs\voice_arpan\Lib\asyncio\streams.py", line 543, in _wait_for_data
    await self._waiter
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\cli\_run.py", line 79, in _worker_run
    await worker.run()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 387, in run
    await self._inference_executor.initialize()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\supervised_proc.py", line 169, in initialize
    init_res = await asyncio.wait_for(
               ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\asyncio\tasks.py", line 502, in wait_for
    raise exceptions.TimeoutError() from exc
TimeoutError
