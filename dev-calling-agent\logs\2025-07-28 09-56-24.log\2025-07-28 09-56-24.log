[ 2025-07-28 09:56:27,834 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 09:56:28,278 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4446 seconds.
[ 2025-07-28 09:56:32,267 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:56:40,676 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:56:40,683 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:56:46,429 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:56:46,430 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 09:56:47,294 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:56:47,298 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 09:56:47,305 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 09:56:47,306 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:56:47,306 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 09:56:47,573 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:56:53,972 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:56:53,977 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:57:00,180 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:57:00,181 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 09:57:00,203 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 09:57:00,797 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 13.23 seconds
[ 2025-07-28 09:57:01,157 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:57:01,159 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:57:06,264 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:57:07,055 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 09:57:19,732 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:57:30,294 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:57:30,961 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:57:30,962 ] 276 root - INFO - Web search tool invoked with query: 'current weather'
[ 2025-07-28 09:57:31,170 ] 168 root - INFO - Detected language: en for text: 'current weather...'
[ 2025-07-28 09:57:31,171 ] 285 root - INFO - Language detected and LOCKED: English (en)
[ 2025-07-28 09:57:31,171 ] 330 root - ERROR - Web search tool error: property 'instructions' of 'LanguageAwareAgent' object has no setter
[ 2025-07-28 09:57:31,172 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:58:01,463 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 09:58:01,465 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 09:58:01,468 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 09:58:01,469 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 09:58:01,470 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
