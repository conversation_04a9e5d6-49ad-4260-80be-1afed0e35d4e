[ 2025-07-26 12:05:35,160 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 12:05:35,580 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4202 seconds.
[ 2025-07-26 12:05:39,530 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 12:05:48,680 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 12:05:48,684 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 12:05:55,868 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 12:05:55,870 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 12:05:56,649 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 12:05:56,655 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 12:05:56,675 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 12:05:56,678 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 12:05:56,679 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 12:05:56,892 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 12:05:57,196 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3039 seconds.
[ 2025-07-26 12:05:57,199 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 12:06:03,981 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 12:06:03,983 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 12:06:11,201 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 12:06:11,204 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 12:06:13,909 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 17.02 seconds
[ 2025-07-26 12:06:14,322 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 12:06:14,323 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 12:06:14,332 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 12:06:14,333 ] 64 root - ERROR - Error Route: 'question'
[ 2025-07-26 12:06:14,335 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 322, in __anext__
    val = await self._event_aiter.__anext__()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopAsyncIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\langchain\langgraph.py", line 90, in _run
    async for message_chunk, _ in self._graph.astream(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\__init__.py", line 2768, in astream
    async for _ in runner.atick(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 401, in atick
    _panic_or_proceed(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 511, in _panic_or_proceed
    raise exc
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\retry.py", line 137, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 440, in ainvoke
    ret = await self.afunc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 617, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 608, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\utils\main_utils.py", line 64, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 65, in route_question
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py] line number [45] error message ['question']
During task with name 'route_question' and id '6511a69d-01f1-f3eb-ed6d-e1f8986a4004'
[ 2025-07-26 12:06:18,138 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 12:06:18,829 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 12:06:18,831 ] 64 root - ERROR - Error Route: 'question'
[ 2025-07-26 12:06:18,833 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 322, in __anext__
    val = await self._event_aiter.__anext__()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopAsyncIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\langchain\langgraph.py", line 90, in _run
    async for message_chunk, _ in self._graph.astream(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\__init__.py", line 2768, in astream
    async for _ in runner.atick(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 401, in atick
    _panic_or_proceed(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 511, in _panic_or_proceed
    raise exc
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\retry.py", line 137, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 440, in ainvoke
    ret = await self.afunc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 617, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 608, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\utils\main_utils.py", line 64, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 65, in route_question
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py] line number [45] error message ['question']
During task with name 'route_question' and id '6a888069-e964-9e50-8083-307dd5c6c5c4'
[ 2025-07-26 12:06:19,467 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 12:06:19,479 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 12:06:19,481 ] 64 root - ERROR - Error Route: 'question'
[ 2025-07-26 12:06:19,483 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 322, in __anext__
    val = await self._event_aiter.__anext__()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopAsyncIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\langchain\langgraph.py", line 90, in _run
    async for message_chunk, _ in self._graph.astream(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\__init__.py", line 2768, in astream
    async for _ in runner.atick(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 401, in atick
    _panic_or_proceed(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 511, in _panic_or_proceed
    raise exc
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\retry.py", line 137, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 440, in ainvoke
    ret = await self.afunc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 617, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 608, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\utils\main_utils.py", line 64, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 65, in route_question
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py] line number [45] error message ['question']
During task with name 'route_question' and id 'b3a71f67-441a-501c-0a09-88f0d5bcc333'
[ 2025-07-26 12:06:45,191 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 12:06:45,197 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 12:06:45,198 ] 64 root - ERROR - Error Route: 'question'
[ 2025-07-26 12:06:45,200 ] 877 livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 45, in route_question
    question = state["question"]
               ~~~~~^^^^^^^^^^^^
KeyError: 'question'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\langchain\langgraph.py", line 90, in _run
    async for message_chunk, _ in self._graph.astream(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\__init__.py", line 2768, in astream
    async for _ in runner.atick(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 401, in atick
    _panic_or_proceed(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 511, in _panic_or_proceed
    raise exc
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\retry.py", line 137, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 440, in ainvoke
    ret = await self.afunc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 617, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 608, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\utils\main_utils.py", line 64, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 65, in route_question
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py] line number [45] error message ['question']
During task with name 'route_question' and id 'a4c79513-028b-81ab-a1db-473fe169b146'
[ 2025-07-26 12:06:45,204 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 322, in __anext__
    val = await self._event_aiter.__anext__()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopAsyncIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\langchain\langgraph.py", line 90, in _run
    async for message_chunk, _ in self._graph.astream(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\__init__.py", line 2768, in astream
    async for _ in runner.atick(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 401, in atick
    _panic_or_proceed(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 511, in _panic_or_proceed
    raise exc
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\retry.py", line 137, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 440, in ainvoke
    ret = await self.afunc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 617, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 608, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\utils\main_utils.py", line 64, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 65, in route_question
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py] line number [45] error message ['question']
During task with name 'route_question' and id 'a4c79513-028b-81ab-a1db-473fe169b146'
[ 2025-07-26 12:06:45,216 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 12:09:34,235 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 12:09:34,239 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 12:09:34,243 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 12:09:34,243 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 12:09:34,243 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
