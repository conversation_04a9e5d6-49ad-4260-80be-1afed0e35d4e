[ 2025-07-26 17:53:39,726 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 17:53:40,186 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4611 seconds.
[ 2025-07-26 17:53:45,424 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 17:53:55,732 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 17:53:55,734 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 17:54:03,826 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 17:54:03,826 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 17:54:04,962 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 17:54:04,967 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 17:54:04,977 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 17:54:04,978 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 17:54:04,980 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 17:54:05,243 ] 206 root - ERROR - voice agent error: name 'create_agentic_rag_tool' is not defined
[ 2025-07-26 17:54:05,244 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 174, in entrypoint
    rag_tool = create_agentic_rag_tool()
               ^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'create_agentic_rag_tool' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 207, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [174] error message [name 'create_agentic_rag_tool' is not defined]
[ 2025-07-26 17:55:17,494 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 17:55:17,496 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 17:55:17,498 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 17:55:17,498 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 17:55:17,499 ] 278 livekit.agents - DEBUG - job exiting
