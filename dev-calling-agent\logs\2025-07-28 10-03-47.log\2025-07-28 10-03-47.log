[ 2025-07-28 10:03:50,508 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 10:03:50,920 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4125 seconds.
[ 2025-07-28 10:03:54,752 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:04:02,783 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 10:04:02,786 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:04:09,150 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 10:04:09,151 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 10:04:09,854 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 10:04:09,857 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 10:04:09,862 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 10:04:09,863 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 10:04:09,864 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 10:04:10,053 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:04:16,070 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 10:04:16,072 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:04:22,206 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 10:04:22,206 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 10:04:22,207 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 10:04:22,223 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:04:27,869 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 10:04:27,872 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:04:33,834 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 10:04:33,834 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 10:04:33,835 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 10:04:34,242 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 24.19 seconds
[ 2025-07-28 10:04:34,585 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 10:04:34,585 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 10:04:35,611 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 10:04:49,292 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 10:04:50,008 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 10:04:50,010 ] 586 root - INFO - Web search tool invoked with query: 'Who is the current Prime Minister of India?'
[ 2025-07-28 10:04:50,219 ] 168 root - INFO - Detected language: en for text: 'Who is the current Prime Minister of India?...'
[ 2025-07-28 10:04:50,219 ] 594 root - INFO - Language detected: English (en)
[ 2025-07-28 10:04:50,219 ] 628 root - ERROR - Web search tool error: property 'instructions' of 'LanguageAwareAgent' object has no setter
[ 2025-07-28 10:04:50,220 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 10:04:50,221 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 10:04:50,222 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 10:04:50,878 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 10:04:50,879 ] 322 root - INFO - Intelligent search invoked with query: 'Who is the current Prime Minister of India?', type: 'auto'
[ 2025-07-28 10:04:50,879 ] 342 root - INFO - Using search strategy: web_only
[ 2025-07-28 10:04:50,879 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 10:04:50,879 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 10:04:50,879 ] 45 root - INFO - \U0001f310 Web search for: 'Who is the current Prime Minister of India?...'
[ 2025-07-28 10:05:01,988 ] 57 root - INFO - Web search completed in 11.11s
[ 2025-07-28 10:05:01,989 ] 68 root - INFO - \u2705 Finished 'search_web' in 11.1096 seconds.
[ 2025-07-28 10:05:01,990 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-127' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 11.11 seconds
[ 2025-07-28 10:05:01,999 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 10:05:11,284 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 10:05:11,284 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 10:05:11,287 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 10:05:11,287 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 10:05:11,287 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
