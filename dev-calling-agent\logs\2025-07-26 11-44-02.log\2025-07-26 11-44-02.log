[ 2025-07-26 11:44:05,653 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:44:06,117 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4636 seconds.
[ 2025-07-26 11:44:10,455 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:44:19,800 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:44:19,804 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:44:25,821 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:44:25,822 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:44:28,541 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:44:28,547 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 11:44:28,558 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 11:44:28,560 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:44:28,560 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 11:44:28,629 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:44:28,959 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3297 seconds.
[ 2025-07-26 11:44:28,963 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:44:36,576 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:44:36,581 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:44:44,044 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:44:44,045 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:44:45,867 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 17.23 seconds
[ 2025-07-26 11:44:46,473 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 11:44:46,484 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 11:44:46,485 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 11:44:46,493 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 11:44:46,494 ] 64 root - ERROR - Error Route: 'question'
[ 2025-07-26 11:44:46,495 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 322, in __anext__
    val = await self._event_aiter.__anext__()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopAsyncIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\langchain\langgraph.py", line 90, in _run
    async for message_chunk, _ in self._graph.astream(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\__init__.py", line 2768, in astream
    async for _ in runner.atick(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 401, in atick
    _panic_or_proceed(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 511, in _panic_or_proceed
    raise exc
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\retry.py", line 137, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 440, in ainvoke
    ret = await self.afunc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 617, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 608, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\utils\main_utils.py", line 64, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 65, in route_question
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py] line number [45] error message ['question']
During task with name 'route_question' and id '1213f5f4-2dc1-4ad4-600c-b26de2fc2b43'
[ 2025-07-26 11:44:47,605 ] 373 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\cartesia\tts.py", line 374, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Invalid response status (status_code=402, request_id=None, body=None, retryable=False)
[ 2025-07-26 11:44:47,853 ] 373 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\cartesia\tts.py", line 374, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Invalid response status (status_code=402, request_id=None, body=None, retryable=False)
[ 2025-07-26 11:44:48,011 ] 1785 asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-30' coro=<ConnectionPool.prewarm.<locals>._prewarm_impl() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\connection_pool.py:163> exception=WSServerHandshakeError(RequestInfo(url=URL('wss://api.cartesia.ai/tts/websocket?api_key=sk_car_xKd1ZkBp4fg2qTYNqpaAmi&cartesia_version=2024-06-10'), method='GET', headers=<CIMultiDictProxy('Host': 'api.cartesia.ai', 'Upgrade': 'websocket', 'Connection': 'Upgrade', 'Sec-WebSocket-Version': '13', 'Sec-WebSocket-Key': 'WGnwLtTKBppHugK+RXi5IQ==', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate', 'User-Agent': 'Python/3.11 aiohttp/3.12.14')>, real_url=URL('wss://api.cartesia.ai/tts/websocket?api_key=sk_car_xKd1ZkBp4fg2qTYNqpaAmi&cartesia_version=2024-06-10')), (), status=402, message='Invalid response status', headers=<CIMultiDictProxy('Date': 'Sat, 26 Jul 2025 06:14:48 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Content-Length': '165', 'Connection': 'keep-alive', 'Vary': 'Origin', 'X-Content-Type-Options': 'nosniff', 'X-Request-Id': '947567ad-ab33-4ffb-b46d-8ba235b2e1f1', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '9651c244ae558fad-KNU')>)>
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\connection_pool.py", line 165, in _prewarm_impl
    conn = await self._connect(timeout=self._connect_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\connection_pool.py", line 61, in _connect
    connection = await self._connect_cb(timeout)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\cartesia\tts.py", line 159, in _connect_ws
    return await asyncio.wait_for(session.ws_connect(url), timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\asyncio\tasks.py", line 489, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\aiohttp\client.py", line 1472, in send
    return self._coro.send(arg)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\aiohttp\client.py", line 1084, in _ws_connect
    raise WSServerHandshakeError(
aiohttp.client_exceptions.WSServerHandshakeError: 402, message='Invalid response status', url='wss://api.cartesia.ai/tts/websocket?api_key=sk_car_xKd1ZkBp4fg2qTYNqpaAmi&cartesia_version=2024-06-10'
[ 2025-07-26 11:44:50,573 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 11:44:50,581 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 11:44:50,584 ] 64 root - ERROR - Error Route: 'question'
[ 2025-07-26 11:44:50,587 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 322, in __anext__
    val = await self._event_aiter.__anext__()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
StopAsyncIteration

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\langchain\langgraph.py", line 90, in _run
    async for message_chunk, _ in self._graph.astream(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\__init__.py", line 2768, in astream
    async for _ in runner.atick(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 401, in atick
    _panic_or_proceed(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\runner.py", line 511, in _panic_or_proceed
    raise exc
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\pregel\retry.py", line 137, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langgraph\utils\runnable.py", line 440, in ainvoke
    ret = await self.afunc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 617, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\langchain_core\runnables\config.py", line 608, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\utils\main_utils.py", line 64, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py", line 65, in route_question
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\src\agent\route.py] line number [45] error message ['question']
During task with name 'route_question' and id '313cb585-79f7-ec40-f10f-1f4fdefe95b2'
[ 2025-07-26 11:44:50,671 ] 373 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\cartesia\tts.py", line 374, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Invalid response status (status_code=402, request_id=None, body=None, retryable=False)
[ 2025-07-26 11:44:50,954 ] 373 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\cartesia\tts.py", line 374, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Invalid response status (status_code=402, request_id=None, body=None, retryable=False)
[ 2025-07-26 11:44:53,131 ] 373 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\cartesia\tts.py", line 374, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Invalid response status (status_code=402, request_id=None, body=None, retryable=False)
[ 2025-07-26 11:44:55,347 ] 21 livekit.agents - ERROR - Error in _tts_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 212, in _tts_inference_task
    async for audio_frame in tts_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 408, in tts_node
    async for ev in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 520, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\cartesia\tts.py", line 374, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Invalid response status (status_code=402, request_id=None, body=None, retryable=False)
[ 2025-07-26 11:45:18,395 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 11:45:18,396 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 11:45:18,401 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 11:45:18,407 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 11:45:18,407 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
