import time
import sys
from src.rag.retrieval import SearchDocument
from src.exception import CustomException
from src.logging.logger import logging
from src.agent.constant import GraphState
from src.utils.main_utils import measure_time

class RetrieverTool:
    def __init__(self,llm):
        self.llm = llm
        self.search_doc = SearchDocument(llm=llm)

        print("=" * 20, ' Retriever Tool Invoked', "=" * 20)

    @measure_time
    def search(self, state: GraphState):
        """High-performance document retrieval with caching"""

        start_time = time.time()
        question = state['question']
        print(question)

        try:
            logging.info(f"🔍 Retrieving documents for: '{question}...'")

            response = self.search_doc.invoke(question)
            print(response)

            logging.info(f"Rag Tool Response: {response}")
            logging.info(f"Time Take: {time.time() - start_time}")

            web_serach = False
            if response is None or "i don't know" in response.lower():
                web_serach = True
          
            
              
           
            print(f"final result : {response} \t web search : \t {web_serach}")
            
            print("=" * 20, ' Retriever Tool Invoked Completed', "=" * 20)
            return {"documents": response, "question": question, "use_web_search": web_serach}

        except Exception as e:
            logging.error(f"Vectore Scarch Error {e}")
            raise CustomException(e,sys)