import os
import sys
import time
import requests
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
from src.exception import CustomException
from src.logging.logger import logging
from src.utils.main_utils import measure_time

load_dotenv()

class WebSearchTool:
    """
    Dedicated web search tool for external information gathering.
    This tool searches the internet for current, real-time information.
    """
    
    def __init__(self):
        self.api_key = os.getenv('TAVLY_API_KEY')
        self.base_url = "https://api.tavily.com/search"
        self.tool_name = "web_search"
        
        if not self.api_key:
            logging.warning("TAVLY_API_KEY not found. Web search functionality may be limited.")
        
        logging.info("Web Search Tool initialized successfully")

    @measure_time
    def search_web(self, query: str, language_context: Optional[str] = None, max_results: int = 3) -> Dict[str, Any]:
        """
        Search the web for current information.
        
        Args:
            query: Search query from user
            language_context: Optional language context for search
            max_results: Maximum number of search results to return
            
        Returns:
            Dictionary containing search results and metadata
        """
        start_time = time.time()
        
        try:
            logging.info(f"🌐 Web search for: '{query[:50]}...'")
            
            # Enhance query with language context if needed
            enhanced_query = self._enhance_query_for_search(query, language_context)
            
            # Perform web search
            search_results = self._perform_search(enhanced_query, max_results)
            
            # Process and format results
            formatted_results = self._format_search_results(search_results)
            
            search_time = time.time() - start_time
            logging.info(f"Web search completed in {search_time:.2f}s")
            
            return {
                "results": formatted_results,
                "query": query,
                "enhanced_query": enhanced_query,
                "search_time": search_time,
                "source": "web_search",
                "language_context": language_context,
                "result_count": len(search_results) if search_results else 0
            }
            
        except Exception as e:
            logging.error(f"Web search error: {e}")
            raise CustomException(e, sys)
    
    def _enhance_query_for_search(self, query: str, language_context: Optional[str]) -> str:
        """
        Enhance the search query for better web search results.
        
        Args:
            query: Original query
            language_context: Language context
            
        Returns:
            Enhanced query string
        """
        enhanced_query = query
        
        # Add language context if it's not English
        if language_context and language_context != 'en':
            enhanced_query = f"{query} (in {language_context})"
        
        # Add current year for time-sensitive queries
        time_sensitive_keywords = ['latest', 'recent', 'current', 'today', 'news', 'update']
        if any(keyword in query.lower() for keyword in time_sensitive_keywords):
            enhanced_query += " 2025"
        
        return enhanced_query
    
    def _perform_search(self, query: str, max_results: int) -> List[Dict]:
        """
        Perform the actual web search using Tavily API.
        
        Args:
            query: Search query
            max_results: Maximum results to return
            
        Returns:
            List of search results
        """
        try:
            payload = {
                "api_key": self.api_key,
                "query": query,
                "search_depth": "advanced",
                "include_answer": True,
                "include_images": False,
                "include_raw_content": False,
                "max_results": max_results
            }
            
            response = requests.post(self.base_url, json=payload, timeout=15)
            response.raise_for_status()
            data = response.json()
            
            return data.get("results", [])
            
        except requests.RequestException as e:
            logging.error(f"Web search API error: {e}")
            return []
        except Exception as e:
            logging.error(f"Unexpected web search error: {e}")
            return []
    
    def _format_search_results(self, search_results: List[Dict]) -> str:
        """
        Format search results into a coherent response.
        
        Args:
            search_results: Raw search results from API
            
        Returns:
            Formatted string with search results
        """
        if not search_results:
            return "No relevant information found in web search."
        
        formatted_content = []
        
        for i, result in enumerate(search_results, 1):
            content = result.get('content', '')
            title = result.get('title', '')
            url = result.get('url', '')
            
            if content:
                formatted_content.append(f"Result {i}: {title}\n{content}\nSource: {url}\n")
        
        return "\n".join(formatted_content)
    
    def get_tool_description(self) -> str:
        """Get description for this tool when used in LLM function calling."""
        return """
        Web Search Tool - Use this tool for:
        
        PRIMARY USE CASES:
        - Current news and events
        - Real-time information and updates
        - Recent developments and breaking news
        - Live data and current market information
        - Weather updates and current conditions
        - Latest product releases or announcements
        - Current stock prices or financial data
        
        WHEN TO USE:
        - Questions about recent events
        - Queries requiring up-to-date information
        - Current news or breaking stories
        - Real-time data requests
        - When vector database doesn't have relevant information
        
        WHEN NOT TO USE:
        - Technical documentation queries
        - Internal company information
        - Historical data that's likely in documents
        - Domain-specific technical questions
        """
    
    def get_search_capabilities(self) -> Dict[str, Any]:
        """Get information about search capabilities."""
        return {
            "tool_type": "web_search",
            "search_method": "internet_search",
            "data_sources": ["web_pages", "news_sites", "public_information"],
            "strengths": [
                "Real-time information",
                "Current events",
                "Broad coverage",
                "Latest updates"
            ],
            "limitations": [
                "May have inaccurate information",
                "Requires internet connection",
                "Limited by search API quotas"
            ]
        }
    
    def is_available(self) -> bool:
        """Check if web search is available."""
        return bool(self.api_key)
