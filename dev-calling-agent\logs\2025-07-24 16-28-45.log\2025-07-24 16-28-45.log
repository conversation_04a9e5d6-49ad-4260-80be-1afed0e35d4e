[ 2025-07-24 16:29:22,630 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:29:34,581 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:29:34,590 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:29:44,052 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:29:44,052 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:29:48,052 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:29:48,120 ] 23 root - INFO - \U0001f50d Retrieving documents for: 'What are the different operational voltages of ABB switch gear and transformer cd dry type?...'
[ 2025-07-24 16:29:52,650 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:29:52,652 ] 109 root - INFO -  RAG Retrival Output: According to the provided context, the operational voltage of ABB switchgear and transformer CD dry type is 145/170 kV.
[ 2025-07-24 16:29:52,652 ] 28 root - INFO - Rag Tool Response: According to the provided context, the operational voltage of ABB switchgear and transformer CD dry type is 145/170 kV.
[ 2025-07-24 16:29:52,652 ] 29 root - INFO - Time Take: 4.533340692520142
[ 2025-07-24 16:29:52,655 ] 47 root - INFO - filter doc started
[ 2025-07-24 16:29:53,300 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:29:53,303 ] 52 root - INFO - 
[ 2025-07-24 16:29:53,303 ] 56 root - INFO - ---------- Relevant Document -----------
[ 2025-07-24 16:29:53,304 ] 66 root - INFO -  Retriever Validation Compleed
[ 2025-07-24 16:29:53,304 ] 67 root - INFO - ============================ Retriever Validator Invoke Completed ============================== 
[ 2025-07-24 16:29:53,305 ] 20 root - INFO - =========================== GenerationTool Invoke ===========================
[ 2025-07-24 16:29:53,307 ] 25 root - INFO - type: binary_score='yes'
[ 2025-07-24 16:29:53,307 ] 31 root - INFO - Reducing the context length
[ 2025-07-24 16:29:53,307 ] 52 root - ERROR - Exception in Generate :'RetrieveDocument' object has no attribute 'page_content'
[ 2025-07-24 16:29:53,307 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\nodes\generation.py] line number [33] error message ['RetrieveDocument' object has no attribute 'page_content']
