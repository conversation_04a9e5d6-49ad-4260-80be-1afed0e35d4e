[ 2025-07-28 09:29:37,473 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 09:29:38,032 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5591 seconds.
[ 2025-07-28 09:29:43,544 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:29:52,644 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:29:52,645 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:29:58,290 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:29:58,291 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 09:29:59,092 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:29:59,094 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 09:29:59,100 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 09:29:59,102 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 09:29:59,102 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:29:59,309 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:30:05,832 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:30:05,834 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:30:12,812 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:30:12,812 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 09:30:12,814 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 09:30:12,835 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:30:20,182 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:30:20,183 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:30:26,470 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:30:26,471 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 09:30:26,472 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 09:30:27,322 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 28.02 seconds
[ 2025-07-28 09:30:27,771 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:30:27,773 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:30:29,112 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 09:30:32,050 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:30:40,934 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:30:44,423 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:30:56,708 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:31:09,227 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:31:21,573 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:31:22,242 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:31:22,242 ] 586 root - INFO - Web search tool invoked with query: 'Who is the current Prime Minister of India'
[ 2025-07-28 09:31:22,487 ] 168 root - INFO - Detected language: en for text: 'Who is the current Prime Minister of India...'
[ 2025-07-28 09:31:22,488 ] 594 root - INFO - Language detected: English (en)
[ 2025-07-28 09:31:22,488 ] 628 root - ERROR - Web search tool error: property 'instructions' of 'LanguageAwareAgent' object has no setter
[ 2025-07-28 09:31:22,490 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:31:23,301 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:31:23,302 ] 322 root - INFO - Intelligent search invoked with query: 'Who is the current Prime Minister of India', type: 'auto'
[ 2025-07-28 09:31:23,302 ] 342 root - INFO - Using search strategy: web_only
[ 2025-07-28 09:31:23,302 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 09:31:23,302 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 09:31:23,303 ] 45 root - INFO - \U0001f310 Web search for: 'Who is the current Prime Minister of India...'
[ 2025-07-28 09:31:36,526 ] 57 root - INFO - Web search completed in 13.22s
[ 2025-07-28 09:31:36,527 ] 68 root - INFO - \u2705 Finished 'search_web' in 13.2254 seconds.
[ 2025-07-28 09:31:36,528 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-295' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 13.23 seconds
[ 2025-07-28 09:31:36,551 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:31:53,307 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:32:04,628 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:32:05,366 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:32:05,366 ] 586 root - INFO - Web search tool invoked with query: 'current weather in Kolkata'
[ 2025-07-28 09:32:05,366 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 09:32:05,367 ] 45 root - INFO - \U0001f310 Web search for: 'current weather in Kolkata...'
[ 2025-07-28 09:32:20,954 ] 126 root - ERROR - Web search API error: HTTPSConnectionPool(host='api.tavily.com', port=443): Read timed out. (read timeout=15)
[ 2025-07-28 09:32:20,955 ] 57 root - INFO - Web search completed in 15.59s
[ 2025-07-28 09:32:20,955 ] 68 root - INFO - \u2705 Finished 'search_web' in 15.5884 seconds.
[ 2025-07-28 09:32:20,957 ] 604 root - INFO - Web search completed with 0 results
[ 2025-07-28 09:32:20,957 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-382' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 15.59 seconds
[ 2025-07-28 09:32:20,968 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:32:21,748 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:32:21,749 ] 586 root - INFO - Web search tool invoked with query: 'Kolkata weather today'
[ 2025-07-28 09:32:21,749 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 09:32:21,750 ] 45 root - INFO - \U0001f310 Web search for: 'Kolkata weather today...'
[ 2025-07-28 09:32:37,355 ] 126 root - ERROR - Web search API error: HTTPSConnectionPool(host='api.tavily.com', port=443): Read timed out. (read timeout=15)
[ 2025-07-28 09:32:37,356 ] 57 root - INFO - Web search completed in 15.61s
[ 2025-07-28 09:32:37,356 ] 68 root - INFO - \u2705 Finished 'search_web' in 15.6064 seconds.
[ 2025-07-28 09:32:37,357 ] 604 root - INFO - Web search completed with 0 results
[ 2025-07-28 09:32:37,358 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-404' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 15.61 seconds
[ 2025-07-28 09:32:37,362 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 09:32:37,363 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 09:32:37,383 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:32:38,204 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:32:38,204 ] 586 root - INFO - Web search tool invoked with query: 'Kolkata weather forecast'
[ 2025-07-28 09:32:38,204 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 09:32:38,204 ] 45 root - INFO - \U0001f310 Web search for: 'Kolkata weather forecast...'
[ 2025-07-28 09:32:49,594 ] 57 root - INFO - Web search completed in 11.39s
[ 2025-07-28 09:32:49,594 ] 68 root - INFO - \u2705 Finished 'search_web' in 11.3899 seconds.
[ 2025-07-28 09:32:49,594 ] 604 root - INFO - Web search completed with 3 results
[ 2025-07-28 09:32:49,596 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-430' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 11.39 seconds
[ 2025-07-28 09:32:49,604 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:33:10,564 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:33:20,046 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:33:20,947 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:33:21,690 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:33:22,325 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:33:22,938 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 09:33:22,939 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 09:33:22,944 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 09:33:22,944 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 09:33:22,946 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
