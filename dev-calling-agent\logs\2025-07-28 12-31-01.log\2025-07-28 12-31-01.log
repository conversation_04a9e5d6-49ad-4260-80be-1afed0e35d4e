[ 2025-07-28 12:31:05,021 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 12:31:05,446 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4248 seconds.
[ 2025-07-28 12:31:09,585 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:31:18,398 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:31:18,401 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:31:25,260 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:31:25,261 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 12:31:26,025 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:31:26,029 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 12:31:26,036 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 12:31:26,037 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:31:26,037 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 12:31:26,238 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:31:33,706 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:31:33,709 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:31:39,915 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:31:39,915 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 12:31:39,939 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 12:31:40,335 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 14.09 seconds
[ 2025-07-28 12:31:40,692 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 12:31:40,693 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 12:31:42,002 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 12:31:43,070 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-07-28 12:31:45,117 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:31:59,198 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:32:09,576 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:32:10,220 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 12:32:10,222 ] 150 root - INFO - Web search: 'weather in Kolkata today'
[ 2025-07-28 12:32:10,448 ] 168 root - INFO - Detected language: so for text: 'weather in Kolkata today...'
[ 2025-07-28 12:32:10,448 ] 227 root - WARNING - Unsupported language code: so. Defaulting to English.
[ 2025-07-28 12:32:10,449 ] 157 root - INFO - Language detected from query: English (en)
[ 2025-07-28 12:32:10,449 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 12:32:10,449 ] 45 root - INFO - \U0001f310 Web search for: 'weather in Kolkata today...'
[ 2025-07-28 12:32:17,083 ] 57 root - INFO - Web search completed in 6.63s
[ 2025-07-28 12:32:17,084 ] 68 root - INFO - \u2705 Finished 'search_web' in 6.6352 seconds.
[ 2025-07-28 12:32:17,084 ] 162 root - INFO - Web search completed with 3 results
[ 2025-07-28 12:32:17,084 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-164' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 6.86 seconds
[ 2025-07-28 12:32:17,086 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 12:32:17,086 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-28 12:32:17,094 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 12:33:01,108 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:33:18,735 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:34:03,536 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 12:34:03,540 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 12:34:03,545 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 12:34:03,547 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 12:34:03,549 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
