[ 2025-07-26 12:14:42,875 ] 633 asyncio - DEBUG - Using proactor: <PERSON><PERSON>p<PERSON><PERSON><PERSON>
[ 2025-07-26 12:14:42,878 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 12:14:42,878 ] 385 livekit.agents - INFO - starting inference executor
[ 2025-07-26 12:14:42,895 ] 167 livekit.agents - INFO - initializing process
[ 2025-07-26 12:14:47,327 ] 77 livekit.agents - DEBUG - initializing inference runner
[ 2025-07-26 12:14:52,850 ] 83 livekit.agents - DEBUG - inference runner initialized
[ 2025-07-26 12:14:52,851 ] 184 livekit.agents - INFO - process initialized
[ 2025-07-26 12:14:52,851 ] 633 asyncio - DEBUG - Using proactor: <PERSON>ocpProactor
[ 2025-07-26 12:14:52,858 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 12:14:52,962 ] 633 asyncio - DEBUG - Using proactor: <PERSON>ocp<PERSON>roactor
[ 2025-07-26 12:14:52,963 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 12:14:53,012 ] 1242 voice-agent - INFO - Connecting to room fake_room
[ 2025-07-26 12:14:53,014 ] 1245 voice-agent - INFO - Starting voice assistant for participant fake_human
[ 2025-07-26 12:14:53,014 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 12:14:53,459 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4440 seconds.
[ 2025-07-26 12:14:57,781 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 12:15:07,125 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 12:15:07,127 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 12:15:13,437 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 12:15:13,437 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 12:15:23,200 ] 19 livekit.agents - WARNING - Running <Task finished name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=NameError("name 'rag_agent' is not defined")> took too long: 30.19 seconds
[ 2025-07-26 12:15:23,201 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\voice_agent.py", line 1255, in entrypoint
    agent=VoiceAssistant(),
          ^^^^^^^^^^^^^^^^
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\voice_agent.py", line 486, in __init__
    llm=langchain.LLMAdapter(rag_agent.workflow),
                             ^^^^^^^^^
NameError: name 'rag_agent' is not defined
[ 2025-07-26 12:21:45,373 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 12:21:45,377 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 12:21:45,382 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 12:21:45,384 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 12:21:45,386 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 12:21:45,393 ] 318 livekit.agents - INFO - process exiting
