import sys
from typing import Literal, Dict, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.pydantic_v1 import BaseModel, Field

from src.prompt import route_prompt
from src.agent.constant import GraphState
from src.exception import CustomException
from src.logging.logger import logging
from src.utils.main_utils import measure_time


# Question Router
class RouteQuery(BaseModel):
    """Route a user query to the most relevant data source."""
    datasource: Literal["vectorstore", "web_search"] = Field(
        ...,
        description="Route the user query to the vectorstore or websearch. Avalable options are 'vectorstore' or 'web_search'",
    )
    confidence: Optional[float] = Field(
        default=None,
        description="Confidence score for the routing decision (0.0 to 1.0)"
    )


class QuestionRouter:
    def __init__(self, llm):
        self.llm = llm 
        
        self.structured_llm_router = llm.with_structured_output(RouteQuery)
        
        router_prompt = ChatPromptTemplate.from_messages(
			[("system", route_prompt), ("human", "{question}")]
		)
        self.question_router = router_prompt | self.structured_llm_router
        
       
        self.toolchain = router_prompt | self.llm


    @measure_time
    def route_question(self, state: GraphState) -> Dict[str, Any]:
        try:
            print("=" * 20, 'Question Routing', "=" * 20)
            question = state["question"]
            print(f"Input: {question}")

            # Use the structured router for better decision making
            routing_decision = self.question_router.invoke({"question": question})
            print(f"Routing decision: {routing_decision}")

            # Extract the datasource from the structured output
            if hasattr(routing_decision, 'datasource'):
                datasource = routing_decision.datasource
            else:
                # Fallback to content-based routing
                source = self.toolchain.invoke({"question": question})
                content = source.content.strip().lower()
                print(f"Fallback routing result: {content}")

                if "web_search" in content:
                    datasource = "web_search"
                elif "vectorstore" in content:
                    datasource = "vectorstore"
                else:
                    # Default to vectorstore for unknown responses
                    datasource = "vectorstore"

            print(f"Final routing decision: {datasource}")

            if datasource == "web_search":
                print("Routing to web search")
                state = {**state, "use_web_search": True}
            else:
                print("Routing to vectorstore")
                state = {**state, "use_web_search": False}

            print(" ================= routing completed =================")
            logging.info(f"Routing completed: {datasource}")
            return state

        except Exception as e:
            logging.error(f"Error Route: {e}")
            raise CustomException(e, sys)
      
            