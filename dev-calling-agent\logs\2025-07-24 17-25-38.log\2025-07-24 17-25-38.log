[ 2025-07-24 17:26:00,777 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 17:26:11,557 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 17:26:11,562 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 17:26:19,727 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 17:26:19,727 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 17:26:22,324 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 400 Bad Request"
[ 2025-07-24 17:26:22,325 ] 50 root - ERROR - Error Route: Error code: 400 - {'error': {'message': "Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': "I don't know."}}
[ 2025-07-24 17:26:22,326 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\route.py] line number [40] error message [Error code: 400 - {'error': {'message': "Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': "I don't know."}}]
