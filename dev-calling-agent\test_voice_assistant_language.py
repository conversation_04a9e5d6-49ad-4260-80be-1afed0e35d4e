#!/usr/bin/env python3
"""
Test script for voice assistant language response functionality.
Tests the complete language detection and response system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.language_detection import LanguageDetector

def test_voice_assistant_language_responses():
    """Test voice assistant language response scenarios."""
    
    detector = LanguageDetector()
    
    # Test scenarios with expected responses
    test_scenarios = [
        {
            'language': 'English',
            'input': 'Hello, can you help me with my computer?',
            'expected_lang': 'en',
            'expected_response_style': 'Natural English'
        },
        {
            'language': 'German',
            'input': '<PERSON><PERSON>, können Sie mir mit meinem Computer helfen?',
            'expected_lang': 'de',
            'expected_response_style': 'Proper German with correct grammar'
        },
        {
            'language': 'French',
            'input': 'Bonjour, pouvez-vous m\'aider avec mon ordinateur?',
            'expected_lang': 'fr',
            'expected_response_style': 'Proper French with correct grammar'
        },
        {
            'language': 'Hindi',
            'input': '<PERSON><PERSON><PERSON>, kya aap meri computer mein madad kar sakte hain?',
            'expected_lang': 'hi',
            'expected_response_style': 'English alphabet transliteration'
        },
        {
            'language': 'Tamil',
            'input': 'Vanakkam, neenga ennoda computer la help panna mudiyuma?',
            'expected_lang': 'ta',
            'expected_response_style': 'English alphabet transliteration'
        },
        {
            'language': 'Telugu',
            'input': 'Namaskaram, meeru naa computer lo sahayam cheyagalara?',
            'expected_lang': 'te',
            'expected_response_style': 'English alphabet transliteration'
        }
    ]
    
    print("🎤 Testing Voice Assistant Language Response System")
    print("=" * 60)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 Test Scenario {i}: {scenario['language']}")
        print(f"User Input: '{scenario['input']}'")
        
        try:
            # Detect language
            lang_code, lang_name, lang_config = detector.detect_language(scenario['input'])
            
            print(f"✅ Detected Language: {lang_name} ({lang_code})")
            print(f"Expected Language: {scenario['language']} ({scenario['expected_lang']})")
            
            # Check detection accuracy
            if lang_code == scenario['expected_lang']:
                print("✅ LANGUAGE DETECTION: CORRECT")
            else:
                print(f"⚠️  LANGUAGE DETECTION: Expected {scenario['expected_lang']}, got {lang_code}")
            
            # Get response instructions
            instructions = detector.get_transliteration_instructions(lang_code)
            response_format = detector.get_response_format_instructions(lang_code)
            lang_type = detector.get_language_type(lang_code)
            
            print(f"📝 Response Style: {scenario['expected_response_style']}")
            print(f"🔧 Language Type: {lang_type}")
            print(f"📋 Response Format: {response_format}")
            
            # Show sample response
            if lang_code in lang_config:
                sample_greeting = lang_config['sample_phrases']['greeting']
                print(f"💬 Sample Response: '{sample_greeting}'")
            
            # Simulate assistant response based on language
            if lang_code == 'en':
                simulated_response = "I can help you with your computer. What specific issue are you experiencing?"
            elif lang_code == 'de':
                simulated_response = "Ich kann Ihnen mit Ihrem Computer helfen. Welches spezifische Problem haben Sie?"
            elif lang_code == 'fr':
                simulated_response = "Je peux vous aider avec votre ordinateur. Quel problème spécifique rencontrez-vous?"
            elif lang_code == 'hi':
                simulated_response = "Main aapke computer mein madad kar sakta hun. Aapko kya specific problem hai?"
            elif lang_code == 'ta':
                simulated_response = "Naan ungaloda computer la help panna mudiyum. Ungalukku enna specific problem irukku?"
            elif lang_code == 'te':
                simulated_response = "Nenu mee computer lo sahayam cheyagalanu. Meeku emi specific problem undi?"
            else:
                simulated_response = "I can help you with your computer."
            
            print(f"🤖 Simulated Assistant Response: '{simulated_response}'")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
        
        print("-" * 50)
    
    # Test session consistency
    print(f"\n🔄 Testing Session Language Consistency")
    print("=" * 40)
    
    # Simulate a conversation in Hindi
    hindi_conversation = [
        "Namaste, main ek technical problem ke bare mein puchna chahta hun",
        "Mera computer slow chal raha hai",
        "Kya aap bata sakte hain ki main kya kar sakta hun?"
    ]
    
    detected_languages = []
    for i, message in enumerate(hindi_conversation, 1):
        lang_code, lang_name, _ = detector.detect_language(message)
        detected_languages.append((lang_code, lang_name))
        print(f"Message {i}: '{message}' → {lang_name} ({lang_code})")
    
    # Check consistency
    first_lang = detected_languages[0][0]
    consistent = all(lang[0] == first_lang for lang in detected_languages)
    
    if consistent:
        print("✅ SESSION CONSISTENCY: All messages detected as same language")
    else:
        print("⚠️  SESSION CONSISTENCY: Language detection varied across messages")
    
    print(f"\n📊 Summary:")
    print(f"✅ Supported Languages: {len(detector.get_supported_languages())}")
    print(f"✅ Indian Languages: {len(detector.indian_languages)}")
    print(f"✅ European Languages: {len(detector.european_languages)}")
    print(f"✅ Keyword-based Detection: Enabled for Indian languages")
    print(f"✅ Transliteration Support: Available for all non-English languages")

if __name__ == "__main__":
    test_voice_assistant_language_responses()
