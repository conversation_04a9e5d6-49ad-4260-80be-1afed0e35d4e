[ 2025-07-28 09:58:08,659 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 09:58:09,068 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4096 seconds.
[ 2025-07-28 09:58:12,968 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:58:22,645 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:58:22,647 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:58:28,866 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:58:28,868 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 09:58:29,712 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:58:29,714 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 09:58:29,719 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 09:58:29,720 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 09:58:29,720 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 09:58:29,918 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:58:36,795 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 09:58:36,797 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 09:58:43,404 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 09:58:43,404 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 09:58:43,419 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 09:58:43,825 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 13.92 seconds
[ 2025-07-28 09:58:44,229 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:58:44,230 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 09:58:45,549 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 09:58:50,662 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:59:12,001 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:59:32,902 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 09:59:33,694 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:59:33,695 ] 276 root - INFO - Web search tool invoked with query: 'current weather'
[ 2025-07-28 09:59:33,902 ] 168 root - INFO - Detected language: en for text: 'current weather...'
[ 2025-07-28 09:59:33,902 ] 285 root - INFO - Language detected and LOCKED: English (en)
[ 2025-07-28 09:59:33,902 ] 330 root - ERROR - Web search tool error: property 'instructions' of 'LanguageAwareAgent' object has no setter
[ 2025-07-28 09:59:33,902 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 09:59:34,645 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 09:59:34,646 ] 276 root - INFO - Web search tool invoked with query: 'current weather in your location'
[ 2025-07-28 09:59:34,647 ] 291 root - INFO - Using locked session language: English (en)
[ 2025-07-28 09:59:34,647 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 09:59:34,648 ] 45 root - INFO - \U0001f310 Web search for: 'current weather in your location...'
[ 2025-07-28 09:59:50,432 ] 126 root - ERROR - Web search API error: HTTPSConnectionPool(host='api.tavily.com', port=443): Read timed out. (read timeout=15)
[ 2025-07-28 09:59:50,433 ] 57 root - INFO - Web search completed in 15.78s
[ 2025-07-28 09:59:50,433 ] 68 root - INFO - \u2705 Finished 'search_web' in 15.7863 seconds.
[ 2025-07-28 09:59:50,433 ] 297 root - INFO - Web search completed with 0 results
[ 2025-07-28 09:59:50,434 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-189' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 15.80 seconds
[ 2025-07-28 09:59:50,451 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 10:00:07,422 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 10:00:07,423 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 10:00:07,426 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 10:00:07,427 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 10:00:07,427 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
