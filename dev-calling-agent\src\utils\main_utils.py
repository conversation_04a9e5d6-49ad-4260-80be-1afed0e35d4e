import re
import time
import functools
from typing import List
from langchain.schema import Document
import unicodedata
from src.logging.logger import logging


def preprocess_string(text: str) -> str:
    """
    Preprocess a string by:
    - Removing extra spaces
    - Lowercasing
    - Removing special characters (excluding alphanumeric and space)
    - Normalizing unicode
    """
    if not isinstance(text, str):
        return ""

    # Normalize unicode characters
    text = unicodedata.normalize("NFKC", text)

    # Lowercase
    text = text.lower()

    # Remove special characters (keep alphanumeric and spaces)
    text = re.sub(r'[^a-z0-9\s]', '', text)

    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text


def preprocess_web_doc(raw_text: str) -> List[Document]:
    """
    Clean and chunk the raw web document text into LangChain Document objects.
    """
    # Step 1: Clean up special characters and formatting issues
    clean_text = re.sub(r"�", "-", raw_text)             # Replace unknown char
    clean_text = re.sub(r"\s{2,}", " ", clean_text)       # Extra whitespace
    clean_text = clean_text.strip()

    # Step 2: Split into logical chunks (e.g., by sections, or every 500 chars)
    chunks = [clean_text[i:i+500] for i in range(0, len(clean_text), 500)]

    # Step 3: Wrap into LangChain Document objects
    documents = [
        Document(page_content=chunk, metadata={"source": "web_doc", "chunk_id": i})
        for i, chunk in enumerate(chunks)
    ]

    return documents



def measure_time(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        logging.info(f"🚀 Starting '{func.__name__}'...")
        
        result = func(*args, **kwargs)
        
        end = time.time()
        duration = end - start
        logging.info(f"✅ Finished '{func.__name__}' in {duration:.4f} seconds.")
        return result
    return wrapper
