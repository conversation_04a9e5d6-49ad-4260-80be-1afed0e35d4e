[ 2025-07-28 11:03:05,149 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 11:03:05,558 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4094 seconds.
[ 2025-07-28 11:03:09,522 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:03:19,407 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:03:19,410 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:03:26,061 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:03:26,062 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 11:03:26,839 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:03:26,841 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 11:03:26,849 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 11:03:26,851 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:03:26,851 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 11:03:27,041 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:03:33,250 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:03:33,254 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:03:40,230 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:03:40,231 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 11:03:40,246 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 11:03:40,625 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 13.59 seconds
[ 2025-07-28 11:03:41,047 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:03:41,048 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:03:42,420 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 11:03:57,900 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:00,171 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:02,709 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:23,810 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:35,401 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:37,908 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:38,785 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:40,934 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:04:41,691 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 11:04:41,691 ] 79 root - INFO - Vector database search: 'Scientist'
[ 2025-07-28 11:04:41,907 ] 168 root - INFO - Detected language: it for text: 'Scientist...'
[ 2025-07-28 11:04:41,908 ] 227 root - WARNING - Unsupported language code: it. Defaulting to English.
[ 2025-07-28 11:04:41,908 ] 86 root - INFO - Language detected: English (en)
[ 2025-07-28 11:04:41,908 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-07-28 11:04:41,909 ] 37 root - INFO - \U0001f50d Vector DB search for: 'Scientist...'
[ 2025-07-28 11:04:43,516 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-b903c643-a891-440c-8159-c0af21944b8b', 'json_data': {'messages': [{'role': 'system', 'content': "You are a Question Answering assistant. Use the following pieces of retrieved context to answer the question. If you don't know the answer, say just 'I don't know' and Use the tool web_search if you don't know the answer or context is missing.. Use three sentences maximum and keep the answer concise.\n\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [5]\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\n7\n\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [5]\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\n7\n\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [5]\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\n7\n\np(z|x). Table 6 shows the results. For FEVER, BM25 performs best, perhaps since FEVER claims are\nheavily entity-centric and thus well-suited for word overlap-based retrieval. Differentiable retrieval\nimproves results on all other tasks, especially for Open-Domain QA, where it is crucial.\nIndex hot-swapping An advantage of non-parametric memory models like RAG is that knowledge\ncan be easily updated at test time. Parametric-only models like T5 or BART need further training to\nupdate their behavior as the world changes. To demonstrate, we build an index using the DrQA [5]\nWikipedia dump from December 2016 and compare outputs from RAG using this index to the newer\nindex from our main results (December 2018). We prepare a list of 82 world leaders who had changed\n7\n\na correct answer being generated, which is not possible with standard extractive approaches, leading\n5"}, {'role': 'user', 'content': 'Scientist'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-28 11:04:43,517 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-28 11:04:45,170 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Mon, 28 Jul 2025 05:34:47 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99114', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '531.599999ms', 'x-request-id': 'req_01k17rdeqqfn3b1jn83baehqa3', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=eR_cc9SiRGs4RxWCveOv3.I9gQl9GfofHwOp7pJ7K2I-1753680887-*******-uclOra7WC_IOVEqSS3doC9Uk5QGjUAMj05VMAqD.vec5VdnrE6gihPH6vxkWoCij19OAbvNebJWI17CH1TRlk90m2afzRDZaaqF1fQ.cxKk; path=/; expires=Mon, 28-Jul-25 06:04:47 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '966202646980d89e-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-28 11:04:45,184 ] 109 root - INFO -  RAG Retrival Output: Based on the provided context, I found a mention of "RAG" which stands for "Reformer-based Architecture for Generation". It is a non-parametric memory model that allows for easy knowledge updates at test time.
[ 2025-07-28 11:04:45,185 ] 51 root - INFO - Vector DB search completed in 3.28s
[ 2025-07-28 11:04:45,185 ] 52 root - INFO - Results relevant: True
[ 2025-07-28 11:04:45,186 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.2784 seconds.
[ 2025-07-28 11:04:45,188 ] 93 root - INFO - Vector database found relevant results
[ 2025-07-28 11:04:45,191 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-298' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 3.50 seconds
[ 2025-07-28 11:04:45,195 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 11:05:00,287 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 11:05:00,288 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 11:05:00,292 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 11:05:00,295 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 11:05:00,296 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
