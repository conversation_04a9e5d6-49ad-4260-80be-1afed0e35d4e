[ 2025-07-24 16:53:13,196 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:53:23,436 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:53:23,440 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:53:32,850 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:53:32,851 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:53:35,294 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 400 Bad Request"
[ 2025-07-24 16:53:35,295 ] 50 root - ERROR - Error Route: Error code: 400 - {'error': {'message': "tool call validation failed: attempted to call tool 'WebSearch' which was not request.tools", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': 'I\'m not familiar with the topics you\'re asking about (rag, electrical transformer, ABB Switchgear & Transformer Termination CD DRY TYPE PLU), so I\'ll use web search to find the answer.\n\n<tool-use>\n{\n    "tool_calls": [\n        {\n            "id": "pending",\n            "type": "function",\n            "function": {\n                "name": "WebSearch"\n            },\n            "parameters": {\n                "query": "What is the capital of France?"\n            }\n        }\n    ]\n}\n</tool-use>'}}
[ 2025-07-24 16:53:35,296 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\route.py] line number [40] error message [Error code: 400 - {'error': {'message': "tool call validation failed: attempted to call tool 'WebSearch' which was not request.tools", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': 'I\'m not familiar with the topics you\'re asking about (rag, electrical transformer, ABB Switchgear & Transformer Termination CD DRY TYPE PLU), so I\'ll use web search to find the answer.\n\n<tool-use>\n{\n    "tool_calls": [\n        {\n            "id": "pending",\n            "type": "function",\n            "function": {\n                "name": "WebSearch"\n            },\n            "parameters": {\n                "query": "What is the capital of France?"\n            }\n        }\n    ]\n}\n</tool-use>'}}]
