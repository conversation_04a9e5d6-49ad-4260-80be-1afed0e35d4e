[ 2025-07-28 11:49:47,753 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 11:49:48,171 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4180 seconds.
[ 2025-07-28 11:49:52,102 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:50:01,195 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:50:01,198 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:50:09,419 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:50:09,420 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 11:50:10,195 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:50:10,199 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 11:50:10,205 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 11:50:10,206 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:50:10,208 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 11:50:10,412 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:50:17,138 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:50:17,142 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:50:24,466 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:50:24,467 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 11:50:24,479 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 11:50:24,881 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 14.47 seconds
[ 2025-07-28 11:50:25,317 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:50:25,317 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:50:26,608 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 11:50:35,335 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:50:50,294 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:51:04,973 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:51:49,177 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:51:54,454 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:52:09,064 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:52:30,741 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:52:53,617 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:53:08,909 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:53:18,585 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:53:30,378 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:53:45,695 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:54:04,701 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:54:15,598 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:54:16,904 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:54:29,428 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:04,387 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:16,951 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:21,121 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:31,428 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:33,621 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:44,743 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:49,071 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:55:52,070 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:56:03,161 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:56:06,900 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 11:56:06,901 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 11:56:06,906 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 11:56:06,914 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 11:56:06,915 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
