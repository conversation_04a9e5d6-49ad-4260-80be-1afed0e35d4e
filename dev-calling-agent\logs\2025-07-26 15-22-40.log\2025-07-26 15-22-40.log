[ 2025-07-26 15:22:43,338 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 15:22:43,768 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4292 seconds.
[ 2025-07-26 15:22:47,567 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:22:56,548 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 15:22:56,551 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:23:02,938 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 15:23:02,938 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 15:23:04,964 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:23:04,968 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 15:23:04,974 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 15:23:04,975 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:23:04,976 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 15:23:07,578 ] 475 livekit.agents - ERROR - failed to update the instructions
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 469, in _start_session
    update_instructions(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 767, in update_instructions
    llm.ChatMessage(id=INSTRUCTIONS_MESSAGE_ID, role="system", content=[instructions]),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for ChatMessage
content.0.ImageContent
  Input should be a valid dictionary or instance of ImageContent [type=model_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.AudioContent
  Input should be a valid dictionary or instance of AudioContent [type=model_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.str
  Input should be a valid string [type=string_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
[ 2025-07-26 15:23:07,721 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:23:07,725 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:23:07,728 ] 146 root - ERROR - voice agent error: sequence item 0: expected str instance, tuple found
[ 2025-07-26 15:23:07,730 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 143, in entrypoint
    await session.generate_reply(instructions="Hello! How may I assist you today?")
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_session.py", line 742, in generate_reply
    handle = self._activity._generate_reply(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 759, in _generate_reply
    instructions = "\n".join([self._agent.instructions, instructions])
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: sequence item 0: expected str instance, tuple found

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 147, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [143] error message [sequence item 0: expected str instance, tuple found]
[ 2025-07-26 15:23:15,001 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:23:15,852 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:23:15,853 ] 82 root - INFO - AgenticRAG tool invoked with query: 'Today's Kolkata's weather forecast'
[ 2025-07-26 15:23:15,853 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:23:15,856 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:23:15,864 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-d1a085c7-5a6f-4046-b59c-7b6632f1c8d4', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': "Today's Kolkata's weather forecast"}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:23:15,866 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:23:16,724 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:53:16 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99924', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '45.599999ms', 'x-request-id': 'req_01k132db63e23976nhvcccs1x6', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=jXhSsU1RZ1guGkhVqMp3V4kfjVG5S5oZYjJycDqdA4w-**********-*******-vK5KeJq3g1Ucj62YM1jXtIVtzlRfSiqVIDpAk6x9Bcyx7OXdtfYPBHtDS6x3qE.Su9TlJVs.ARpDc9vT.RHKW3Fhfrf6eqC9wNaVg_IYiNk; path=/; expires=Sat, 26-Jul-25 10:23:16 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9653024dcb3e4467-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:23:16,735 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:23:16,735 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.8786 seconds.
[ 2025-07-26 15:23:16,737 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:23:16,737 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:23:16,737 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:23:17,799 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:23:17,800 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:23:17,802 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:23:17,804 ] 68 root - INFO - \u2705 Finished 'run' in 1.9510 seconds.
[ 2025-07-26 15:23:17,804 ] 99 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:23:17,809 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:23:18,418 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:23:18,420 ] 82 root - INFO - AgenticRAG tool invoked with query: 'Kolkata weather'
[ 2025-07-26 15:23:18,421 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:23:18,424 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:23:18,428 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-e72e9e87-c4ee-423b-a799-9bbc4fcd2b5f', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'Kolkata weather'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:23:18,433 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:23:18,917 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:53:19 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99929', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '42.6ms', 'x-request-id': 'req_01k132ddfve23twn9eg6xynfqf', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9653025c889d4467-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:23:18,920 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:23:18,920 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.4956 seconds.
[ 2025-07-26 15:23:18,922 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:23:18,922 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:23:18,923 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:23:19,920 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:23:19,920 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:23:19,921 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:23:19,921 ] 68 root - INFO - \u2705 Finished 'run' in 1.5006 seconds.
[ 2025-07-26 15:23:19,922 ] 99 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:23:19,924 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:23:20,478 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 15:24:34,641 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 15:24:34,644 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 15:24:34,649 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 15:24:34,650 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 15:24:34,650 ] 278 livekit.agents - DEBUG - job exiting
