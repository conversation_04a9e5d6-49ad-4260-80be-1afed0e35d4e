import sys
from typing import  Dict, Any
from langchain.pydantic_v1 import BaseModel, Field
from langchain.prompts import Chat<PERSON>romptTemplate

from src.agent.constant import GraphState
from src.exception import CustomException
from src.logging.logger import logging
from src.prompt import GRADE_PROMPT, hallucination_prompt
from src.utils.main_utils import measure_time



class  RetrieveDocument(BaseModel):
    """Binary score for the relevance check of retrieved documents"""
    binary_score: str = Field(
        description="Documents are relevant to the question? 'yes' or 'no'"
    )

class  RetrieverValidator:
    def __init__(self, llm):
        self.llm = llm
        self.structured_output = RetrieveDocument
        logging.info("============================ Retriever Validator Invoke ============================== ")

    def _validation_chain(self, question: str, document: str):
        structured_llm = self.llm.with_structured_output(self.structured_output)
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", GRADE_PROMPT),
            ("human", "Retrieved document: \n\n {document} \n\n User question: {question}")
        ])

        chain = prompt | structured_llm
        response = chain.invoke({"question": question, "document": document})
        return response

    @measure_time
    def validate_documents(self, state: GraphState) -> Dict[str, Any]:
        
        print("=" * 20, 'Document Validation Tool Invoked', "=" * 20)

        question = state["question"]
        documents = state['documents']
       

        try:
           
            logging.info("filter doc started")
            filtered_docs = []
            use_web_search = False
            result = self._validation_chain(question, documents)

            logging.info(f"Relavent doc response : {documents}")

            grade = result.binary_score
            if grade.lower() == "yes":
                print("---------- Relevant Document -----------")
                logging.info("---------- Relevant Document -----------")
                filtered_docs.append(result)
                print(filtered_docs)

            else:
                print("---------- Not Relevant Document -----------")
                logging.info("---------- Not Relevant Document -----------")
                use_web_search = True
            
            print("=" * 20, 'Document Validation Completed', "=" * 20)
            logging.info(" Retriever Validation Compleed")
            logging.info("============================ Retriever Validator Invoke Completed ============================== ")
            
            return {
            "documents": documents,
            "use_web_search": use_web_search,
            "question": question,
            }


        except Exception as e:
            logging.error(f"Exception in  Retriever Validator :{e}")
            raise CustomException(e,sys)
        


class GradeHallucinations(BaseModel):
    """Binary score for hallucination present in generated answer."""
    binary_score: str= Field( ...,
        description="Is the answer grounded in the given facts? 'yes' or 'no'."
    )
    explanation: str = Field( ...,
        description="Explain why the answer is or isn't grounded in the facts."
    )


class HallucinationGrader:
    def __init__(self, llm):
        self.llm = llm
        self.structured_llm_grader = GradeHallucinations

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", hallucination_prompt),
            ("human", "Set of facts:\n\n{documents}\n\nLLM generation:\n\n{generation}")
        ])

        self.chain = self.prompt | self.llm.with_structured_output(self.structured_llm_grader)

    def hallucination_chain(self, documents: str, generation: str):
        return self.chain.invoke({"generation": generation, "documents": documents})

    def grade_hallucination(self, state: GraphState) -> Dict[str, Any]:
        try:
            print("=" * 20, 'Hallucination Grading', "=" * 20)
            logging.info("============================ Hallucination Grader Invoke ============================== ")
            
            documents = state['documents']
            generation = state.get("generation", "")
        

            
            result = self.hallucination_chain(documents, generation)
            
            logging.info(f"Hallucination check: {result.binary_score}")
            logging.info(f"Explanation: {result.explanation}")
            
            
            if result.binary_score:
                print("---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---")
                print("---CHECK ANSWER---")
                score = self.chain.invoke({"documents": documents, "generation": generation})

                if score.binary_score:
                    print("---DECISION: ANSWER ADDRESSES THE USER QUESTION---")
                    state["use_web_search"] = False
                else:
                    print("---DECISION: ANSWER DOES NOT ADDRESS THE USER QUESTION---")
                    state["use_web_search"] = True
            else:
                print("---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS---")
                state["use_web_search"] = True
                logging.info("Generation contains hallucinations - triggering web search")
            
            return state
            
        except Exception as e:
            logging.error(f"Error Execution on Hallucination Grader: {e}")
            raise CustomException(e,sys)
        finally:
            logging.info("============================ Hallucination Grader Invoke Completed ============================== ")