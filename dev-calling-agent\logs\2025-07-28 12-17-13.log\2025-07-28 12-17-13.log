[ 2025-07-28 12:17:17,185 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 12:17:17,611 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4267 seconds.
[ 2025-07-28 12:17:21,597 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:17:29,622 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:17:29,626 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:17:36,309 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:17:36,310 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 12:17:37,353 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:17:37,356 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 12:17:37,356 ] 385 livekit.agents - INFO - starting inference executor
[ 2025-07-28 12:17:37,381 ] 167 livekit.agents - INFO - initializing process
[ 2025-07-28 12:17:47,385 ] 248 livekit.agents - INFO - killing process
[ 2025-07-28 12:17:47,386 ] 81 livekit.agents - ERROR - worker failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\asyncio\tasks.py", line 500, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\channel.py", line 47, in arecv_message
    return _read_message(await dplx.recv_bytes(), messages)
                         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\aio\duplex_unix.py", line 35, in recv_bytes
    len_bytes = await self._reader.readexactly(4)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\asyncio\streams.py", line 750, in readexactly
    await self._wait_for_data('readexactly')
  File "D:\conda\envs\voice_arpan\Lib\asyncio\streams.py", line 543, in _wait_for_data
    await self._waiter
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\cli\_run.py", line 79, in _worker_run
    await worker.run()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 387, in run
    await self._inference_executor.initialize()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\supervised_proc.py", line 169, in initialize
    init_res = await asyncio.wait_for(
               ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\asyncio\tasks.py", line 502, in wait_for
    raise exceptions.TimeoutError() from exc
TimeoutError
