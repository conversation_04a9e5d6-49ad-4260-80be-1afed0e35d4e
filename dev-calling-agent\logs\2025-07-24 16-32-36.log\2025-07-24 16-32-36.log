[ 2025-07-24 16:32:57,860 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:33:07,512 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:33:07,518 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:33:15,300 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:33:15,301 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:33:17,238 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:33:17,273 ] 23 root - INFO - \U0001f50d Retrieving documents for: 'What are the different operational voltages of ABB switch gear and transformer cd dry type?...'
[ 2025-07-24 16:33:21,135 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:33:21,140 ] 109 root - INFO -  RAG Retrival Output: The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:33:21,140 ] 28 root - INFO - Rag Tool Response: The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:33:21,140 ] 29 root - INFO - Time Take: 3.867673397064209
[ 2025-07-24 16:33:21,141 ] 47 root - INFO - filter doc started
[ 2025-07-24 16:33:21,946 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:33:21,948 ] 52 root - INFO - Relavent doc response : The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:33:21,949 ] 57 root - INFO - ---------- Relevant Document -----------
[ 2025-07-24 16:33:21,949 ] 67 root - INFO -  Retriever Validation Compleed
[ 2025-07-24 16:33:21,950 ] 68 root - INFO - ============================ Retriever Validator Invoke Completed ============================== 
[ 2025-07-24 16:33:21,952 ] 20 root - INFO - =========================== GenerationTool Invoke ===========================
[ 2025-07-24 16:33:21,953 ] 25 root - INFO - Input: The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:33:21,954 ] 31 root - INFO - Reducing the context length
[ 2025-07-24 16:33:21,954 ] 52 root - ERROR - Exception in Generate :cannot access local variable 'context' where it is not associated with a value
[ 2025-07-24 16:33:21,955 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\nodes\generation.py] line number [37] error message [cannot access local variable 'context' where it is not associated with a value]
