[ 2025-07-24 17:51:31,951 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 17:51:41,711 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 17:51:41,714 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 17:51:49,003 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 17:51:49,003 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 17:51:50,941 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 400 Bad Request"
[ 2025-07-24 17:51:50,943 ] 73 root - ERROR - Error Route: Error code: 400 - {'error': {'message': "Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': "I don't know the answer to that question."}}
[ 2025-07-24 17:51:50,944 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\route.py] line number [59] error message [Error code: 400 - {'error': {'message': "Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': "I don't know the answer to that question."}}]
