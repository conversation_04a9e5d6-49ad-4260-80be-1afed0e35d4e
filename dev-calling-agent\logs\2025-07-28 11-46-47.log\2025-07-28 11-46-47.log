[ 2025-07-28 11:46:50,155 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 11:46:50,572 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4176 seconds.
[ 2025-07-28 11:46:55,206 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:47:04,905 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:47:04,909 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:47:14,702 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:47:14,703 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 11:47:15,545 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:47:15,548 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 11:47:15,552 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 11:47:15,553 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 11:47:15,554 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:47:15,759 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:47:25,480 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:47:25,484 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:47:33,216 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:47:33,216 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 11:47:33,231 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 11:47:33,686 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 17.94 seconds
[ 2025-07-28 11:47:33,850 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:47:33,850 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 11:47:34,958 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 11:47:50,973 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:48:12,773 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:48:32,157 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:48:33,183 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:48:58,171 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:49:17,352 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 11:49:35,745 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 11:49:35,749 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 11:49:35,753 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 11:49:35,769 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 11:49:35,771 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
