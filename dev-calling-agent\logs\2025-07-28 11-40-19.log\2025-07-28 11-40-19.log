[ 2025-07-28 11:40:22,369 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 11:40:22,784 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4152 seconds.
[ 2025-07-28 11:40:26,710 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:40:36,449 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:40:36,451 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:40:43,947 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:40:43,947 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 11:40:44,899 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:40:44,902 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 11:40:44,907 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 11:40:44,908 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 11:40:44,908 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 11:40:45,108 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:40:52,763 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 11:40:52,766 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 11:40:59,444 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 11:40:59,445 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 11:40:59,471 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 11:40:59,720 ] 358 root - ERROR - Voice agent error: LLM.__init__() got an unexpected keyword argument 'system_prompt'
[ 2025-07-28 11:40:59,720 ] 19 livekit.agents - WARNING - Running <Task finished name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=CustomException(TypeError("LLM.__init__() got an unexpected keyword argument 'system_prompt'"))> took too long: 14.62 seconds
[ 2025-07-28 11:40:59,721 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 307, in entrypoint
    llm=groq.LLM(
        ^^^^^^^^^
TypeError: LLM.__init__() got an unexpected keyword argument 'system_prompt'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 359, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [307] error message [LLM.__init__() got an unexpected keyword argument 'system_prompt']
[ 2025-07-28 11:45:03,374 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 11:45:03,376 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 11:45:03,380 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-28 11:45:03,381 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 11:45:03,382 ] 278 livekit.agents - DEBUG - job exiting
