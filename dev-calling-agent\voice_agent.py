from livekit.agents import (
    Agent,
    AgentSession,
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
)
from livekit.plugins import (
    langchain,
    groq,
    silero,
    speechify,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from src.agent.agent import AgenticRAG

import logging
import asyncio
import re
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv(dotenv_path=".env")

# Setup logging
logger = logging.getLogger("voice-agent")
logger.setLevel(logging.DEBUG)

# Simple language detection
try:
    from langdetect import detect, LangDetectError
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False
    logger.warning("langdetect not installed. Language detection disabled.")


# Simple language mapping
LANGUAGE_CODE_MAPPING = {
    'hi': 'hindi',
    'en': 'english',
    'bn': 'bengali',
    'ta': 'tamil',
    'te': 'telugu',
    'mr': 'marathi',
    'gu': 'gujarati',
}

def detect_language(text: str) -> str:
    """Simple language detection"""
    if not text or len(text.strip()) < 2:
        return 'english'

    if not LANGDETECT_AVAILABLE:
        return 'english'

    try:
        detected = detect(text)
        return LANGUAGE_CODE_MAPPING.get(detected, 'english')
    except (LangDetectError, Exception):
        return 'english'



class VoiceAssistant(Agent):
    def __init__(self):
        self.detected_language = 'english'

        # Initialize RAG agent
    #     rag_agent = AgenticRAG()

        super().__init__(
            instructions=(
                "You are a helpful voice AI assistant. "
                "Respond naturally and conversationally. "
                "Keep responses concise for voice interaction. "
                "Use the available tools to provide accurate information."
            ),

            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
            ),

            llm=groq.LLM(
                model="llama3-70b-8192",
                
            ),

            tts=speechify.TTS(
                model="simba-multilingual",
            ),

            turn_detection=MultilingualModel(),
            tools=[self.create_query_tool()],
        )

    def create_query_tool(self):
        """Create a simple query tool that uses the RAG agent"""
        @llm.function_tool
        async def query_tool(query: str) -> str:
            """
            Process user queries using the RAG system.
            Handles both simple questions and complex information retrieval.
            """
            try:
                logger.info(f"Processing query: {query}")

                # Detect language for better response
                self.detected_language = detect_language(query)
                logger.info(f"Detected language: {self.detected_language}")

                # For now, return a simple response since the RAG workflow is handled by the LLM adapter
                return f"I understand your query: '{query}'. Let me help you with that."

            except Exception as e:
                logger.error(f"Query tool failed: {e}")
                return "I'm sorry, I encountered an error while processing your request. Please try again."

        return query_tool


def prewarm(proc):
    proc.userdata["vad"] = silero.VAD.load()


async def entrypoint(ctx: JobContext):
    logger.info(f"Connecting to room {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    participant = await ctx.wait_for_participant()
    logger.info(f"Starting voice assistant for participant {participant.identity}")

    session = AgentSession(
        vad=ctx.proc.userdata["vad"],
        min_endpointing_delay=0.5,
        max_endpointing_delay=60.0,
    )

    await session.start(
        room=ctx.room,
        agent=VoiceAssistant(),
    )


if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
            drain_timeout=90,
        )
    )