import os
import sys
import async<PERSON>
from typing import Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
import time
from src.agent.agent import AgenticRAG
from src.logging.logger import logging
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import AgentSession, Agent, JobContext, llm
from livekit.plugins import silero, groq, speechify
from dotenv import load_dotenv

load_dotenv()

# Global variables for session language tracking and caching
session_language = None
session_language_config = None

# Pre-initialize components for faster startup
print("🚀 Pre-loading components for ultra-fast responses...")
start_time = time.time()

# Use ThreadPoolExecutor for parallel initialization
with ThreadPoolExecutor(max_workers=3) as executor:
    rag_future = executor.submit(lambda: AgenticRAG())
    lang_future = executor.submit(lambda: LanguageDetector())

    rag_agent = rag_future.result()
    language_detector = lang_future.result()

print(f"✅ Components loaded in {time.time() - start_time:.2f}s")

# Cache for language detection results
@lru_cache(maxsize=100)
def cached_language_detection(text_hash: str, text: str):
    """Cached language detection for repeated patterns"""
    return language_detector.detect_language(text)

def get_language_specific_instructions(lang_code: str, lang_config: dict) -> str:
    """Generate language-specific instructions for the agent."""
    if lang_code == 'en':
        return """
You are a helpful voice AI assistant. Respond in clear, natural English.

CRITICAL RULES:
- For greetings (hello, hi, how are you): Respond directly WITHOUT using tools
- For technical questions: Use vector_database_search
- For current events/news/weather: Use web_search
- Keep responses conversational and concise for voice interaction

TOOLS AVAILABLE:
- vector_database_search: For technical documentation and internal knowledge
- web_search: For current news and real-time information
"""

    lang_name = lang_config.get('name', 'Unknown')
    sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '')
    sample_error = lang_config.get('sample_phrases', {}).get('error', '')

    return f"""
You are a helpful multilingual voice AI assistant.

🚨 ABSOLUTE LANGUAGE RULE 🚨
- You MUST respond ONLY in {lang_name} using English alphabet
- NEVER use English words in your responses
- Example greeting: '{sample_greeting}'
- Example error: '{sample_error}'

CRITICAL RESPONSE RULES:
- For greetings (namaste, vanakkam, hello): Respond directly in {lang_name} WITHOUT using tools
- For technical questions: Use vector_database_search
- For current events/news/weather: Use web_search
- Always respond in {lang_name} using English alphabet transliteration

TOOLS AVAILABLE:
- vector_database_search: For technical documentation and internal knowledge
- web_search: For current news and real-time information

EXAMPLES:
- User says "Namaste" → You respond: "{sample_greeting}" (NO TOOLS NEEDED)
- User asks technical question → Use vector_database_search
- User asks about weather → Use web_search
"""

# Pre-create tools for faster access
print("🔧 Pre-creating tools...")
vector_tool_instance = VectorDatabaseTool(rag_agent.llm)
web_tool_instance = WebSearchTool()

# Pre-compiled language responses for ultra-fast lookup
FAST_RESPONSES = {
    'hi': {
        'no_results': "Mere paas is technical sawal ka jawaab nahi hai.",
        'error': "Knowledge base mein kuch technical problem hai."
    },
    'ta': {
        'no_results': "Enakku indha technical kelvikku information illa.",
        'error': "Knowledge base la konjam technical problem irukku."
    },
    'te': {
        'no_results': "Naa daggara ee technical prashnaku information ledu.",
        'error': "Knowledge base lo konni technical samasyalu unnaayi."
    },
    'de': {
        'no_results': "Ich habe keine technischen Informationen dazu.",
        'error': "Es gibt ein technisches Problem mit der Wissensdatenbank."
    },
    'fr': {
        'no_results': "Je n'ai pas d'informations techniques à ce sujet.",
        'error': "Il y a un problème technique avec la base de connaissances."
    },
    'en': {
        'no_results': "I don't have technical information about this.",
        'error': "Technical issue with knowledge base."
    }
}

def create_vector_database_tool():
    """Create an ultra-fast vector database search tool."""

    @llm.function_tool(
        name="vector_database_search",
        description="ONLY for technical questions about electrical machines, transformers, motors, company policies. NEVER use for greetings, weather, news, or simple conversations. Use ONLY when user asks specific technical questions."
    )
    async def vector_database_search(query: str) -> str:
        """Search internal vector database for technical documentation."""
        start_time = time.time()

        try:
            global session_language, session_language_config

            # Ultra-fast language detection with caching
            if session_language is None:
                query_hash = str(hash(query))
                lang_code, lang_name, lang_config = cached_language_detection(query_hash, query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"⚡ Fast language detection: {lang_name} ({lang_code})")

            # Parallel search execution
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                vector_tool_instance.search_documents,
                query,
                session_language or 'en'
            )

            # Ultra-fast response selection
            lang = session_language or 'en'
            if result['is_relevant']:
                response = result['results']
                logging.info(f"✅ Vector search: {time.time() - start_time:.2f}s")
            else:
                response = FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['no_results']
                logging.info(f"❌ No results: {time.time() - start_time:.2f}s")

            # Minimal language enforcement for speed
            if lang != 'en' and session_language_config:
                sample = session_language_config.get('sample_phrases', {}).get('greeting', '')
                if sample:
                    response = f"[{session_language_config['name']}] {response}"

            return response

        except Exception as e:
            logging.error(f"Vector tool error: {e}")
            lang = session_language or 'en'
            return FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['error']

    return vector_database_search

def create_web_search_tool():
    """Create an ultra-fast web search tool."""

    @llm.function_tool(
        name="web_search",
        description="ONLY for current news, weather, real-time information, recent events. NEVER use for greetings, technical documentation, or simple conversations. Use ONLY when user asks about current/live information."
    )
    async def web_search(query: str) -> str:
        """Search the web for current information."""
        start_time = time.time()

        try:
            global session_language, session_language_config

            # Ultra-fast language detection with caching
            if session_language is None:
                query_hash = str(hash(query))
                lang_code, lang_name, lang_config = cached_language_detection(query_hash, query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"⚡ Fast language detection: {lang_name} ({lang_code})")

            # Parallel web search execution
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                web_tool_instance.search_web,
                query,
                session_language or 'en'
            )

            response = result['results']
            logging.info(f"🌐 Web search: {time.time() - start_time:.2f}s, {result['result_count']} results")

            # Minimal language enforcement for speed
            lang = session_language or 'en'
            if lang != 'en' and session_language_config:
                sample = session_language_config.get('sample_phrases', {}).get('greeting', '')
                if sample:
                    response = f"[{session_language_config['name']}] {response}"

            return response

        except Exception as e:
            logging.error(f"Web search error: {e}")
            lang = session_language or 'en'
            # Fast error responses
            web_errors = {
                'hi': "Web search mein technical problem hai.",
                'ta': "Web search la technical problem irukku.",
                'te': "Web search lo technical problem undi.",
                'de': "Technisches Problem bei der Websuche.",
                'fr': "Problème technique avec la recherche web.",
                'en': "Technical issue with web search."
            }
            return web_errors.get(lang, web_errors['en'])

    return web_search

class UltraFastLanguageAgent(Agent):
    """Ultra-optimized Agent with lightning-fast language detection and response."""

    def __init__(self, llm, tools):
        # Minimal, focused instructions for maximum speed
        initial_instructions = """⚡ ULTRA-FAST MULTILINGUAL AI ⚡

SPEED PRIORITY: Respond in 2-3 seconds maximum

LANGUAGE RULES:
- Detect user language ONCE and lock it for session
- Respond in SAME language using English alphabet
- Hindi → Hindi (English alphabet)
- Tamil → Tamil (English alphabet)
- Telugu → Telugu (English alphabet)
- German → German
- French → French
- English → English

TOOL RULES (FAST):
- Greeting → Direct response (NO TOOLS)
- Technical → vector_database_search
- News/Current → web_search

SPEED TARGETS:
- Language detection: <0.1s
- Tool execution: <2s
- Total response: <3s"""

        super().__init__(
            llm=llm,
            instructions=initial_instructions,
            tools=tools
        )
        self.current_language = None
        self.language_config = None
        self.response_cache = {}  # Simple response caching

    async def _handle_user_message(self, message):
        """Ultra-fast message handling with optimized language detection."""
        global session_language, session_language_config

        start_time = time.time()

        if message.content:
            # Lightning-fast language detection with caching
            if session_language is None:
                msg_hash = str(hash(message.content))
                lang_code, lang_name, lang_config = cached_language_detection(msg_hash, message.content)

                if lang_code != 'unknown':
                    session_language = lang_code
                    session_language_config = lang_config
                    self.current_language = lang_code
                    self.language_config = lang_config
                    logging.info(f"⚡ LANGUAGE LOCKED: {lang_name} ({lang_code}) in {time.time() - start_time:.3f}s")
            else:
                lang_code = session_language
                lang_name = session_language_config.get('name', 'Unknown') if session_language_config else 'Unknown'
                logging.info(f"🔒 USING LOCKED LANGUAGE: {lang_name}")

            # Ultra-minimal instruction updates for speed
            if lang_code and lang_code != 'unknown':
                if lang_code == 'en':
                    self.instructions = "ENGLISH MODE: Direct responses. Greetings=NO TOOLS. Technical=vector_search. News=web_search. FAST!"
                else:
                    sample = lang_config.get('sample_phrases', {}).get('greeting', '') if lang_config else ''
                    self.instructions = f"FAST {lang_name.upper()}: Respond ONLY in {lang_name}. Example: '{sample}'. NO ENGLISH WORDS!"

        return await super()._handle_user_message(message)

async def entrypoint(ctx: JobContext):
    """Ultra-fast entrypoint for the voice agent."""
    global session_language, session_language_config

    try:
        print("🚀 Starting ultra-fast voice agent...")
        start_time = time.time()

        await ctx.connect()

        # Reset session language for new session
        session_language = None
        session_language_config = None

        # Use pre-created tools for instant access
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        # Optimized session configuration for speed
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                detect_language=True,
            ),
            llm=groq.LLM(
                model="llama3-70b-8192",
                temperature=0.05,  # Ultra-low temperature for fastest responses
                max_tokens=150,    # Limit tokens for speed
            ),
            tts=speechify.TTS(
                model="simba-multilingual",
                speed=1.2,  # Slightly faster speech
            ),
        )

        # Create ultra-fast agent
        agent = UltraFastLanguageAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )

        await session.start(agent=agent, room=ctx.room)

        print(f"✅ Agent ready in {time.time() - start_time:.2f}s")

        # Ultra-brief greeting for immediate response
        await session.generate_reply(
            instructions="Say: 'Hello! Ready to help!' Keep it under 5 words."
        )

    except Exception as e:
        logging.error(f"Voice agent error: {e}")
        raise CustomException(e, sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
