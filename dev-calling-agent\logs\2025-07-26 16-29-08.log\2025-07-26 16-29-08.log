[ 2025-07-26 16:29:12,572 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 16:29:12,994 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4218 seconds.
[ 2025-07-26 16:29:17,478 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 16:29:26,469 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 16:29:26,473 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 16:29:32,850 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 16:29:32,851 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 16:29:33,644 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 16:29:33,649 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 16:29:33,679 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 16:29:33,681 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 16:29:33,681 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 16:29:34,936 ] 475 livekit.agents - ERROR - failed to update the instructions
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 469, in _start_session
    update_instructions(
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 767, in update_instructions
    llm.ChatMessage(id=INSTRUCTIONS_MESSAGE_ID, role="system", content=[instructions]),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 3 validation errors for ChatMessage
content.0.ImageContent
  Input should be a valid dictionary or instance of ImageContent [type=model_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.AudioContent
  Input should be a valid dictionary or instance of AudioContent [type=model_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
content.0.str
  Input should be a valid string [type=string_type, input_value=("You are a sophisticated...n in transliteration.",), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
[ 2025-07-26 16:29:34,946 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 16:29:34,947 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 16:29:34,948 ] 150 root - ERROR - voice agent error: sequence item 0: expected str instance, tuple found
[ 2025-07-26 16:29:34,949 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 147, in entrypoint
    await session.generate_reply(instructions="Hello! How may I assist you today?")
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_session.py", line 742, in generate_reply
    handle = self._activity._generate_reply(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 759, in _generate_reply
    instructions = "\n".join([self._agent.instructions, instructions])
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: sequence item 0: expected str instance, tuple found

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\callagent-timeout\dev-calling-agent\langgraph-agent.py", line 151, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\langgraph-agent.py] line number [147] error message [sequence item 0: expected str instance, tuple found]
[ 2025-07-26 16:29:42,699 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 16:29:49,052 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 16:29:49,945 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 16:29:49,945 ] 86 root - INFO - AgenticRAG tool invoked with query: ''
[ 2025-07-26 16:29:49,946 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 16:29:49,952 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 16:29:49,962 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-aa637ceb-0709-4f4e-85e0-08f49d9ab894', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': ''}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 16:29:49,962 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 16:29:50,556 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 10:59:50 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99932', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '40.799999ms', 'x-request-id': 'req_01k13677jrf2krwv1c0agn9ywn', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=XEiNLIEOA2T.7ElLPKGpRqZ9BhVqfNxEXEbiInekIck-**********-*******-IbCiCXOeuIA9.2cAfIpu8Ht7yX3GCUwfRNOcJpLWWEpNKmga6XiEiW79FkV.69truf6_ynDwGSpxXuJZxStWyspgci3hUJ9yzNgLS5PES1Q; path=/; expires=Sat, 26-Jul-25 11:29:50 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '965363d03809aa12-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 16:29:50,568 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 16:29:50,569 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.6169 seconds.
[ 2025-07-26 16:29:50,572 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 16:29:50,572 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 16:29:50,572 ] 59 root - INFO - Web Search Started
[ 2025-07-26 16:29:51,668 ] 48 root - ERROR - Exception raise: 400 Client Error: Bad Request for url: https://api.tavily.com/search
[ 2025-07-26 16:29:51,668 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [400 Client Error: Bad Request for url: https://api.tavily.com/search]
[ 2025-07-26 16:29:51,669 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [400 Client Error: Bad Request for url: https://api.tavily.com/search]]
[ 2025-07-26 16:29:51,670 ] 68 root - INFO - \u2705 Finished 'run' in 1.7239 seconds.
[ 2025-07-26 16:29:51,670 ] 103 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 16:29:51,674 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 16:29:52,290 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 16:29:52,291 ] 86 root - INFO - AgenticRAG tool invoked with query: ''
[ 2025-07-26 16:29:52,292 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 16:29:52,293 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 16:29:52,295 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-9f446a71-ccbc-4efd-bbee-dcbbe6a1a993', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': ''}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 16:29:52,295 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 16:29:52,922 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 10:59:53 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99932', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '40.799999ms', 'x-request-id': 'req_01k13679qaf2mvmp8defmkkjxv', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '965363ddfbacaa12-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 16:29:52,925 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 16:29:52,925 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.6320 seconds.
[ 2025-07-26 16:29:52,925 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 16:29:52,927 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 16:29:52,927 ] 59 root - INFO - Web Search Started
[ 2025-07-26 16:29:53,959 ] 48 root - ERROR - Exception raise: 400 Client Error: Bad Request for url: https://api.tavily.com/search
[ 2025-07-26 16:29:53,961 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [400 Client Error: Bad Request for url: https://api.tavily.com/search]
[ 2025-07-26 16:29:53,961 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [400 Client Error: Bad Request for url: https://api.tavily.com/search]]
[ 2025-07-26 16:29:53,962 ] 68 root - INFO - \u2705 Finished 'run' in 1.6696 seconds.
[ 2025-07-26 16:29:53,962 ] 103 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 16:29:53,964 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 16:29:54,565 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 16:30:07,626 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 16:31:08,293 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 16:31:18,550 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 16:31:18,551 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 16:31:18,558 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 16:31:18,567 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-07-26 16:31:18,613 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 16:31:18,614 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
