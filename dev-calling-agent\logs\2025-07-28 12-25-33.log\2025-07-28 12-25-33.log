[ 2025-07-28 12:25:36,859 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 12:25:37,304 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4442 seconds.
[ 2025-07-28 12:25:41,283 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:25:49,249 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:25:49,251 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:25:55,723 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:25:55,723 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 12:25:56,683 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:25:56,688 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 12:25:56,696 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 12:25:56,697 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:25:56,697 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 12:25:56,894 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:26:03,610 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:26:03,612 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:26:09,590 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:26:09,590 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 12:26:09,614 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 12:26:10,021 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 13.12 seconds
[ 2025-07-28 12:26:10,407 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 12:26:10,408 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 12:26:11,491 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 12:26:15,256 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:26:28,084 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:26:53,419 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 12:26:53,421 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 12:26:53,425 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 12:26:53,425 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 12:26:53,425 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
