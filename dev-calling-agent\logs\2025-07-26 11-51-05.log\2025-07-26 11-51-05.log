[ 2025-07-26 11:51:09,275 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:51:09,735 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4600 seconds.
[ 2025-07-26 11:51:14,188 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:51:22,615 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:51:22,619 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:51:28,877 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:51:28,877 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:51:29,906 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:51:29,911 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 11:51:29,918 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 11:51:29,919 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 11:51:29,919 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:51:30,127 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:51:30,455 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3280 seconds.
[ 2025-07-26 11:51:30,457 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:51:36,751 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:51:36,754 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:51:43,803 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:51:43,804 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:51:45,834 ] 76 root - ERROR - voice agent error: name 'speechify' is not defined
[ 2025-07-26 11:51:45,834 ] 19 livekit.agents - WARNING - Running <Task finished name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=CustomException(NameError("name 'speechify' is not defined"))> took too long: 15.70 seconds
[ 2025-07-26 11:51:45,834 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 65, in entrypoint
    tts=speechify.TTS(
        ^^^^^^^^^
NameError: name 'speechify' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 77, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py] line number [65] error message [name 'speechify' is not defined]
[ 2025-07-26 11:52:15,522 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 11:52:15,524 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 11:52:15,526 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 11:52:15,528 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 11:52:15,529 ] 278 livekit.agents - DEBUG - job exiting
