[ 2025-07-26 15:12:01,629 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 15:12:02,034 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4059 seconds.
[ 2025-07-26 15:12:06,006 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:12:16,047 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 15:12:16,050 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 15:12:22,102 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 15:12:22,102 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 15:12:23,174 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:12:23,178 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 15:12:23,186 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 15:12:23,189 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 15:12:23,189 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 15:12:24,141 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:12:24,141 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 15:12:25,569 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 15:12:29,493 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:12:30,156 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:12:30,156 ] 103 root - INFO - AgenticRAG tool invoked with query: 'Today's Kolkata is like the air'
[ 2025-07-26 15:12:30,157 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:12:30,165 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:12:30,181 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-a73a0085-6b68-41fd-9d1d-60f661094ebb', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': "Today's Kolkata is like the air"}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:12:30,182 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:12:30,817 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:42:31 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99925', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '45ms', 'x-request-id': 'req_01k131smmjf6qavc294gaa5p7r', 'via': '1.1 google', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=eySuJqlwvQXWOfgGrFaEeXqlFbDPATG.fXJr3atv7B4-**********-*******-kflvipslz9owAZOPSDFfib2OskIIodubrbTjHDrFk1TPCi6e4HLKawgMARjR5EECGl5E.gfo2zfCqnB7kLjQgYhYtU4RewLm8kRJR4YMObY; path=/; expires=Sat, 26-Jul-25 10:12:31 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9652f28a4dd3ff77-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:12:30,827 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:12:30,827 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.6619 seconds.
[ 2025-07-26 15:12:30,829 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:12:30,829 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:12:30,829 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:12:32,032 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:12:32,032 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:12:32,033 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:12:32,033 ] 68 root - INFO - \u2705 Finished 'run' in 1.8761 seconds.
[ 2025-07-26 15:12:32,034 ] 120 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:12:32,035 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:12:52,312 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:12:53,010 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:12:53,011 ] 103 root - INFO - AgenticRAG tool invoked with query: 'What do you want to see'
[ 2025-07-26 15:12:53,011 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:12:53,015 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:12:53,019 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-876e1da0-f07a-404d-850e-00fea02d51ae', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'What do you want to see'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:12:53,023 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:12:53,736 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:42:53 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99927', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '43.8ms', 'x-request-id': 'req_01k131tayff749mwhyyyz1trs3', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9652f3190e764737-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:12:53,740 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:12:53,740 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.7252 seconds.
[ 2025-07-26 15:12:53,742 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:12:53,742 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:12:53,743 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:12:54,633 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:12:54,633 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:12:54,634 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:12:54,634 ] 68 root - INFO - \u2705 Finished 'run' in 1.6230 seconds.
[ 2025-07-26 15:12:54,635 ] 120 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:12:54,638 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:13:00,402 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:13:01,207 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:13:01,210 ] 103 root - INFO - AgenticRAG tool invoked with query: 'Today, Kolkata is in a bad condition'
[ 2025-07-26 15:13:01,211 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:13:01,216 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:13:01,224 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-feedef8d-7a7e-400e-8868-9bbc4d47a36c', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'Today, Kolkata is in a bad condition'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:13:01,228 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:13:01,872 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:43:02 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99923', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '46.2ms', 'x-request-id': 'req_01k131tjytfxzv7t914rera2r1', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9652f34c3c4e6ee9-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:13:01,874 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:13:01,875 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.6604 seconds.
[ 2025-07-26 15:13:01,878 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:13:01,878 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:13:01,880 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:13:02,815 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:13:02,817 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:13:02,817 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:13:02,817 ] 68 root - INFO - \u2705 Finished 'run' in 1.6062 seconds.
[ 2025-07-26 15:13:02,817 ] 120 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:13:02,818 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-26 15:13:02,820 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-26 15:13:02,822 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:13:11,778 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:13:12,607 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:13:12,610 ] 103 root - INFO - AgenticRAG tool invoked with query: 'Today, Kolkata is in a bad condition due to the holiday people'
[ 2025-07-26 15:13:12,611 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:13:12,616 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:13:12,621 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-509c4e12-5e55-460c-970f-b063a6c19268', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'Today, Kolkata is in a bad condition due to the holiday people'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:13:12,623 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:13:13,328 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:43:13 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99917', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '49.8ms', 'x-request-id': 'req_01k131ty3rf7b9ev3d0ctnbzq6', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9652f393aaaa48e0-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:13:13,332 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:13:13,332 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.7157 seconds.
[ 2025-07-26 15:13:13,333 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:13:13,333 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:13:13,333 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:13:14,247 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:13:14,250 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:13:14,250 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:13:14,251 ] 68 root - INFO - \u2705 Finished 'run' in 1.6399 seconds.
[ 2025-07-26 15:13:14,251 ] 120 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:13:14,252 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-26 15:13:14,254 ] 392 livekit.plugins.silero - WARNING - inference is slower than realtime
[ 2025-07-26 15:13:14,263 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:13:19,372 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:13:20,122 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:13:20,123 ] 103 root - INFO - AgenticRAG tool invoked with query: 'I would say'
[ 2025-07-26 15:13:20,123 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:13:20,125 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:13:20,128 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-197243ad-563f-40b5-8c89-5c87820a3967', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'I would say'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:13:20,129 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:13:20,875 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:43:21 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99930', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '41.999999ms', 'x-request-id': 'req_01k131v5dyf7d9gmdm9hp37d0p', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9652f3c28d90bc9a-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:13:20,880 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:13:20,881 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.7554 seconds.
[ 2025-07-26 15:13:20,882 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:13:20,883 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:13:20,884 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:13:22,313 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:13:22,313 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:13:22,314 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:13:22,315 ] 68 root - INFO - \u2705 Finished 'run' in 2.1919 seconds.
[ 2025-07-26 15:13:22,315 ] 120 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:13:22,316 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-360' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 2.19 seconds
[ 2025-07-26 15:13:22,320 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:13:45,972 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 15:13:46,773 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 15:13:46,774 ] 103 root - INFO - AgenticRAG tool invoked with query: 'Aaj ke Banglaar koto Tariq'
[ 2025-07-26 15:13:46,774 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 15:13:46,777 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 15:13:46,780 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-c07b7b99-bdf9-448a-82cc-59639e4082bb', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'Aaj ke Banglaar koto Tariq'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 15:13:46,781 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 15:13:47,615 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 09:43:47 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99926', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '44.399999ms', 'x-request-id': 'req_01k131vzn2f7n9csda82z6dkps', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9652f46a4e26afb0-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 15:13:47,618 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 15:13:47,619 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.8414 seconds.
[ 2025-07-26 15:13:47,620 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 15:13:47,621 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 15:13:47,621 ] 59 root - INFO - Web Search Started
[ 2025-07-26 15:13:48,712 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 15:13:48,713 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 15:13:48,713 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 15:13:48,714 ] 68 root - INFO - \u2705 Finished 'run' in 1.9402 seconds.
[ 2025-07-26 15:13:48,714 ] 120 root - INFO - AgenticRAG tool response: None
[ 2025-07-26 15:13:48,718 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 15:14:29,471 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 15:14:29,476 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 15:14:29,480 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 15:14:29,480 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-26 15:14:29,480 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
