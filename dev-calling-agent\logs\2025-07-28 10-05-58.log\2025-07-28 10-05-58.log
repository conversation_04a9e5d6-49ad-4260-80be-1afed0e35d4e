[ 2025-07-28 10:06:01,618 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 10:06:02,032 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4146 seconds.
[ 2025-07-28 10:06:05,883 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:06:14,948 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 10:06:14,950 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:06:21,573 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 10:06:21,574 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 10:06:22,366 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 10:06:22,369 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 10:06:22,377 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 10:06:22,378 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 10:06:22,379 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 10:06:22,576 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:06:28,423 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 10:06:28,425 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:06:34,371 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 10:06:34,371 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 10:06:34,371 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 10:06:34,388 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:06:41,516 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 10:06:41,519 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 10:06:47,693 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 10:06:47,694 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 10:06:47,695 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 10:06:48,097 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 25.53 seconds
[ 2025-07-28 10:06:48,468 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 10:06:48,468 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 10:06:49,463 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 10:06:52,731 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 10:07:06,041 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 10:07:20,989 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 10:07:21,726 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 10:07:21,726 ] 586 root - INFO - Web search tool invoked with query: 'Who is the Prime Minister of India?'
[ 2025-07-28 10:07:21,932 ] 168 root - INFO - Detected language: en for text: 'Who is the Prime Minister of India?...'
[ 2025-07-28 10:07:21,932 ] 594 root - INFO - Language detected: English (en)
[ 2025-07-28 10:07:21,934 ] 628 root - ERROR - Web search tool error: property 'instructions' of 'LanguageAwareAgent' object has no setter
[ 2025-07-28 10:07:21,934 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 10:07:22,683 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-28 10:07:22,684 ] 322 root - INFO - Intelligent search invoked with query: 'Who is the current Prime Minister of India', type: 'auto'
[ 2025-07-28 10:07:22,684 ] 342 root - INFO - Using search strategy: web_only
[ 2025-07-28 10:07:22,685 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 10:07:22,686 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-28 10:07:22,688 ] 45 root - INFO - \U0001f310 Web search for: 'Who is the current Prime Minister of India...'
[ 2025-07-28 10:07:38,266 ] 57 root - INFO - Web search completed in 15.58s
[ 2025-07-28 10:07:38,266 ] 68 root - INFO - \u2705 Finished 'search_web' in 15.5801 seconds.
[ 2025-07-28 10:07:38,266 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-193' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 15.58 seconds
[ 2025-07-28 10:07:38,275 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-28 10:08:00,326 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 10:08:00,327 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 10:08:00,330 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 10:08:00,330 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 10:08:00,331 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
