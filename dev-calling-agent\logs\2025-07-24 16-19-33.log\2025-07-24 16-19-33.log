[ 2025-07-24 16:19:57,210 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:20:07,022 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:20:07,026 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:20:14,597 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:20:14,597 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:20:17,455 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:20:17,498 ] 23 root - INFO - \U0001f50d Retrieving documents for: 'What are the different operational voltages of ABB switch gear and transformer cd dry type?...'
[ 2025-07-24 16:20:21,958 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:20:21,960 ] 109 root - INFO -  RAG Retrival Output: According to the provided context, the operational voltage of ABB switchgear and transformer CD dry type is 145/170 kV.
[ 2025-07-24 16:20:21,960 ] 28 root - INFO - Rag Tool Response: According to the provided context, the operational voltage of ABB switchgear and transformer CD dry type is 145/170 kV.
[ 2025-07-24 16:20:21,960 ] 29 root - INFO - Time Take: 4.461474895477295
[ 2025-07-24 16:20:21,961 ] 47 root - INFO - filter doc started
[ 2025-07-24 16:20:23,802 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:20:23,803 ] 54 root - INFO - ---------- Relevant Document -----------
[ 2025-07-24 16:20:23,804 ] 64 root - INFO -  Retriever Validation Compleed
[ 2025-07-24 16:20:23,804 ] 65 root - INFO - ============================ Retriever Validator Invoke Completed ============================== 
[ 2025-07-24 16:20:23,805 ] 20 root - INFO - =========================== GenerationTool Invoke ===========================
[ 2025-07-24 16:20:23,805 ] 122 root - INFO - Agent Weorflow Error: 'list' object has no attribute 'page_content'
