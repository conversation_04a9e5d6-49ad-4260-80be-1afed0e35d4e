[ 2025-07-26 11:41:43,389 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:41:43,857 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4681 seconds.
[ 2025-07-26 11:41:48,145 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:41:56,891 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:41:56,893 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:42:04,376 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:42:04,377 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:42:05,395 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:42:05,405 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 11:42:05,433 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 11:42:05,438 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 11:42:05,438 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 11:42:05,492 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 11:42:05,840 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3478 seconds.
[ 2025-07-26 11:42:05,843 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:42:13,181 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 11:42:13,183 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 11:42:19,708 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 11:42:19,709 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 11:42:20,788 ] 70 root - ERROR - voice agent error: Deepgram API key is required
[ 2025-07-26 11:42:20,788 ] 19 livekit.agents - WARNING - Running <Task finished name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=CustomException(ValueError('Deepgram API key is required'))> took too long: 15.30 seconds
[ 2025-07-26 11:42:20,788 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 60, in entrypoint
    stt=deepgram.STT(model="nova-3", interim_results=True),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\deepgram\stt.py", line 137, in __init__
    raise ValueError("Deepgram API key is required")
ValueError: Deepgram API key is required

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py", line 71, in entrypoint
    raise CustomException(e,sys)
src.exception.CustomException: Error occurred python script name [D:\Downloads\dev-calling-agent\dev-calling-agent\langgraph-agent.py] line number [60] error message [Deepgram API key is required]
[ 2025-07-26 11:43:57,413 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 11:43:57,415 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 11:43:57,420 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 11:43:57,420 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 11:43:57,422 ] 278 livekit.agents - DEBUG - job exiting
