[ 2025-07-24 16:35:02,083 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:35:12,031 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-24 16:35:12,035 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-24 16:35:19,719 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-24 16:35:19,721 ] 23 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-24 16:35:21,756 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:35:21,791 ] 23 root - INFO - \U0001f50d Retrieving documents for: 'What are the different operational voltages of ABB switch gear and transformer cd dry type?...'
[ 2025-07-24 16:35:25,749 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:35:25,750 ] 109 root - INFO -  RAG Retrival Output: The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:35:25,751 ] 28 root - INFO - Rag Tool Response: The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:35:25,751 ] 29 root - INFO - Time Take: 3.9597740173339844
[ 2025-07-24 16:35:25,752 ] 47 root - INFO - filter doc started
[ 2025-07-24 16:35:26,320 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:35:26,326 ] 52 root - INFO - Relavent doc response : The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:35:26,327 ] 57 root - INFO - ---------- Relevant Document -----------
[ 2025-07-24 16:35:26,330 ] 67 root - INFO -  Retriever Validation Compleed
[ 2025-07-24 16:35:26,330 ] 68 root - INFO - ============================ Retriever Validator Invoke Completed ============================== 
[ 2025-07-24 16:35:26,333 ] 20 root - INFO - =========================== GenerationTool Invoke ===========================
[ 2025-07-24 16:35:26,334 ] 25 root - INFO - Input: The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:35:26,334 ] 31 root - INFO - Reducing the context length
[ 2025-07-24 16:35:26,334 ] 33 root - INFO - Genearation Context: The operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:35:26,334 ] 34 root - INFO - Reduce Context length: 93
[ 2025-07-24 16:35:26,740 ] 1025 httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
[ 2025-07-24 16:35:26,747 ] 43 root - INFO - Generation Output :
	 According to the context, the operational voltages of ABB switchgear and transformer CD dry type are 145 kV and 170 kV.
[ 2025-07-24 16:35:26,747 ] 44 root - INFO - =========================== GenerationTool Invoke Completed ===========================
[ 2025-07-24 16:35:26,749 ] 111 root - INFO - ============================ Hallucination Grader Invoke ============================== 
[ 2025-07-24 16:35:26,749 ] 143 root - ERROR - Error Execution on Hallucination Grader: 'str' object has no attribute 'page_content'
[ 2025-07-24 16:35:26,750 ] 146 root - INFO - ============================ Hallucination Grader Invoke Completed ============================== 
[ 2025-07-24 16:35:26,750 ] 122 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\GENAIProjects\calling-repasentive-ai\calling-agent\src\agent\nodes\grade.py] line number [113] error message ['str' object has no attribute 'page_content']
