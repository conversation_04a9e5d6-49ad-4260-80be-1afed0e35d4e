# Ultra-Fast Optimized Retrieval
import sys
import torch
import time
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import threading
from typing import List, Dict, Any
import asyncio

from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_pinecone import PineconeVectorStore
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.llms import BaseLLM

from src.exception import CustomException
from src.logging.logger import logging
from src.prompt import system_prompt

# Global cache for embeddings and retriever
_embedding_cache = None
_retriever_cache = None
_chain_cache = None



@lru_cache(maxsize=1)
def get_cached_embeddings():
    """Get cached embeddings instance for ultra-fast reuse"""
    global _embedding_cache
    if _embedding_cache is None:
        print("🔧 Loading embeddings (one-time setup)...")
        _embedding_cache = HuggingFaceEmbeddings(
            model_name='sentence-transformers/all-MiniLM-L6-v2',
            model_kwargs={'device': 'gpu'} if torch.cuda.is_available() else {'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True, 'batch_size': 64}  # Increased batch size
        )
        print("✅ Embeddings cached!")
    return _embedding_cache

@lru_cache(maxsize=1)
def get_cached_retriever():
    """Get cached retriever instance for ultra-fast reuse"""
    global _retriever_cache
    if _retriever_cache is None:
        print("🔧 Loading vector database (one-time setup)...")
        embeddings = get_cached_embeddings()
        vector_db = PineconeVectorStore.from_existing_index(
            index_name="agentic-rag",
            embedding=embeddings
        )
        _retriever_cache = vector_db.as_retriever(
            search_type="similarity",
            search_kwargs={'k': 3}  # Reduced for speed
        )
        print("✅ Retriever cached!")
    return _retriever_cache

class UltraFastSearchDocument:
    """Ultra-optimized search document with aggressive caching and parallel processing"""

    def __init__(self, llm: BaseLLM, max_workers: int = 8) -> None:
        self.llm = llm
        self.max_workers = max_workers

        # Ultra-fast initialization using cached components
        print("⚡ Ultra-fast initialization...")
        start_time = time.time()

        # Use cached components for instant access
        self.retriever = get_cached_retriever()
        self.vectordb_retriver = self.retriever  # Backward compatibility

        # Create optimized chain
        self._create_fast_chain()

        # Response cache for repeated queries
        self.response_cache = {}
        self.cache_lock = threading.Lock()

        print(f"✅ Ultra-fast init complete: {time.time() - start_time:.2f}s")
        logging.info("Ultra-Fast SearchDocument initialized")

    def _create_fast_chain(self):
        """Create optimized RAG chain with minimal overhead"""
        global _chain_cache

        if _chain_cache is None:
            # Simplified prompt for faster processing
            self.prompt = ChatPromptTemplate.from_messages([
                ("system", "Answer based on context. Be concise and accurate."),
                ("human", "{input}")
            ])

            qa_chain = create_stuff_documents_chain(self.llm, self.prompt)
            _chain_cache = create_retrieval_chain(self.retriever, qa_chain)

        self.rag_chain = _chain_cache
        logging.info("Fast RAG chain ready")

    @lru_cache(maxsize=50)
    def cached_similarity_search(self, query_hash: str, input_text: str) -> str:
        """Cached cosine similarity search for repeated queries"""
        try:
            response = self.vectordb_retriver.invoke(input_text)
            result = "\n\n".join([doc.page_content for doc in response])
            logging.info(f"⚡ Cached similarity search: {len(response)} docs")
            return result
        except Exception as e:
            logging.error(f"Similarity search error: {e}")
            raise CustomException(e, sys)

    def cosin_similarity(self, input_text: str) -> str:
        """Ultra-fast cosine similarity with caching"""
        query_hash = str(hash(input_text))
        return self.cached_similarity_search(query_hash, input_text)

    @lru_cache(maxsize=50)
    def cached_rag_invoke(self, query_hash: str, input_text: str) -> str:
        """Cached RAG chain invocation for repeated queries"""
        try:
            response = self.rag_chain.invoke({"input": input_text})
            result = str(response['answer'])
            logging.info(f"⚡ Cached RAG response generated")
            return result
        except Exception as e:
            logging.error(f"RAG invoke error: {e}")
            raise CustomException(e, sys)

    def invoke(self, input_text: str) -> str:
        """Ultra-fast query processing with aggressive caching"""
        start_time = time.time()

        # Use hash-based caching for ultra-fast repeated queries
        query_hash = str(hash(input_text))

        # Check in-memory cache first
        with self.cache_lock:
            if query_hash in self.response_cache:
                cached_result = self.response_cache[query_hash]
                logging.info(f"🚀 Cache hit! Response in {time.time() - start_time:.3f}s")
                return cached_result

        # Generate new response
        result = self.cached_rag_invoke(query_hash, input_text)

        # Cache the result
        with self.cache_lock:
            self.response_cache[query_hash] = result
            # Keep cache size manageable
            if len(self.response_cache) > 100:
                # Remove oldest entries
                oldest_keys = list(self.response_cache.keys())[:20]
                for key in oldest_keys:
                    del self.response_cache[key]

        logging.info(f"⚡ New response generated in {time.time() - start_time:.2f}s")
        return result

# Backward compatibility alias
SearchDocument = UltraFastSearchDocument
        

