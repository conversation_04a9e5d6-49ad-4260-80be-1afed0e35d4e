# Modify
import sys
import torch
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import threading
from typing import List

from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_pinecone import PineconeVectorStore
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.llms import BaseLLM

from src.exception import CustomException
from src.logging.logger import logging
from src.prompt import system_prompt



class SearchDocument:
    def __init__(self,llm: BaseLLM, max_workers: int = 6) -> None:
        self.max_workers = max_workers
        self.llm = llm
        
        # Simple thread lock for safety
        self.lock = threading.Lock()
        
        # Load components in parallel (faster startup)
        self._parallel_init()

        logging.info("Embedding Load Successfullt")
		# Load retriever
        self.retriever=self._load_retriever()
        
        # Create the RAG chain
        self._create_chain()
    
    def _parallel_init(self):
        """Load LLM and retriever at the same time"""
        print("Loading components...")
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Start loading  components simultaneously
            retriever_future = executor.submit(self._load_retriever)
            
            # Wait for  complete
            self.retriever = retriever_future.result()
            
        print("Components loaded!")
        
    
    def _load_retriever(self):
        """Load the vector database"""
        print("  Loading vector database...")

        embeddings = HuggingFaceEmbeddings(
            model_name='sentence-transformers/all-MiniLM-L6-v2',
            model_kwargs={'device': 'gpu'} if torch.cuda.is_available() else {'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True, 'batch_size': 32}
        )

        vector_db = PineconeVectorStore.from_existing_index(
            index_name="agentic-rag",
            embedding=embeddings
        )

        self.vectordb_retriver = vector_db.as_retriever(
            search_type="similarity",
            search_kwargs={'k': 5}
        )
        return self.vectordb_retriver

    
    def _create_chain(self):
        """Create the RAG chain"""
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", "{input}")
        ])
        
        qa_chain = create_stuff_documents_chain(self.llm, self.prompt)
        self.rag_chain = create_retrieval_chain(self.retriever, qa_chain)
        logging.info("Retrivel Chain Completed")
      
    def cosin_similarity(self, input_text: str) -> str:
        """Get documents using cosine similarity from the retriever"""
        try:
            with self.lock:  # Thread safety if used across threads
                response = self.vectordb_retriver.invoke(input_text)
                response ="\n\n".join([doc.page_content for doc in response])

                logging.info(f"Cosine Similarity Output: {str(response)}")

                return str(response)

        except Exception as e:
            logging.error(f"Cosine Similarity Error: {str(e)}")
            raise CustomException(e, sys)


    def invoke(self, input_text: str) -> str:
        """Process a single query"""
        try:
            with self.lock:  # Thread safety
                response = self.rag_chain.invoke({"input": input_text})

                logging.info(f" RAG Retrival Output: {str(response['answer'])}")

                return str(response['answer'])
            
        except Exception as e:
             logging.error(f"Error: {str(e)}")
             raise CustomException(e,sys)
        

