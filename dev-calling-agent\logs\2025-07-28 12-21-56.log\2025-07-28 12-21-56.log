[ 2025-07-28 12:22:00,220 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-28 12:22:00,724 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5038 seconds.
[ 2025-07-28 12:22:05,399 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:22:13,975 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:22:13,976 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:22:20,195 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:22:20,196 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-28 12:22:20,854 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:22:20,857 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-28 12:22:20,865 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-28 12:22:20,865 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-28 12:22:20,866 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-28 12:22:21,061 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:22:27,187 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-28 12:22:27,189 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-28 12:22:33,313 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-28 12:22:33,314 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-28 12:22:33,331 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-28 12:22:33,815 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 12.77 seconds
[ 2025-07-28 12:22:34,198 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 12:22:34,199 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-28 12:22:35,524 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-28 12:22:39,830 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:22:41,576 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:22:41,583 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-07-28 12:22:49,667 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:23:02,592 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:23:13,057 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:23:15,626 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:23:29,110 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-28 12:24:14,109 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-28 12:24:14,111 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-28 12:24:14,114 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-07-28 12:24:14,114 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-28 12:24:14,115 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
