[ 2025-07-26 14:19:13,886 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-26 14:19:14,466 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5792 seconds.
[ 2025-07-26 14:19:20,862 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 14:19:32,426 ] 33 root - INFO - Embedding Load Successfullt
[ 2025-07-26 14:19:32,428 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-26 14:19:41,032 ] 85 root - INFO - Retrivel Chain Completed
[ 2025-07-26 14:19:41,033 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-26 14:19:42,188 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 14:19:42,192 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-26 14:19:42,199 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-26 14:19:42,200 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-07-26 14:19:42,200 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-26 14:19:43,239 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 14:19:43,240 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-07-26 14:19:44,932 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-07-26 14:19:49,631 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 14:20:02,976 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 14:20:33,658 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 14:20:34,479 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 666, in _run
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\openai\_streaming.py", line 152, in __aiter__
    async for item in self._iterator:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\openai\_streaming.py", line 198, in __stream__
    raise APIError(
openai.APIError: Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 699, in _run
    raise APIConnectionError(retryable=retryable) from e
livekit.agents._exceptions.APIConnectionError: Connection error. (body=None, retryable=True)
[ 2025-07-26 14:20:35,668 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 14:20:35,668 ] 25 root - INFO - Processing RAG query: What is the current weather in Kolkata
[ 2025-07-26 14:20:35,669 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 14:20:35,679 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 14:20:35,693 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-478a0432-eb10-4dc0-8f88-f72922f70f14', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'What is the current weather in Kolkata'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 14:20:35,694 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 14:20:36,484 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 08:50:36 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99923', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '46.2ms', 'x-request-id': 'req_01k12ytk6ref1rzvjhmqd5bdbh', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=pe485ZpFJPRbBdvIpKbRiy6_D.cMOaGYHyBBbx.T1g0-**********-*******-V7iPsRtgXbMhxM72n5eF9ODbAlWJ8Xnf6mPyI4.qbYg_fVVOgNQtlkRXSjh7NZWy5I6lcGSEfOTALsaqjPvZzT6W0SZgGeEphtHVW13TFME; path=/; expires=Sat, 26-Jul-25 09:20:36 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9652a6811bd8ffa5-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 14:20:36,496 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 14:20:36,496 ] 68 root - INFO - \u2705 Finished 'route_question' in 0.8167 seconds.
[ 2025-07-26 14:20:36,500 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 14:20:36,501 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 14:20:36,501 ] 59 root - INFO - Web Search Started
[ 2025-07-26 14:20:37,994 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 14:20:37,994 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 14:20:37,995 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 14:20:37,995 ] 68 root - INFO - \u2705 Finished 'run' in 2.3265 seconds.
[ 2025-07-26 14:20:37,995 ] 40 root - ERROR - RAG query tool failed: 'NoneType' object has no attribute 'get'
[ 2025-07-26 14:20:37,997 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-183' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 2.33 seconds
[ 2025-07-26 14:20:38,000 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 14:20:48,312 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 14:21:02,230 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 14:21:13,833 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-07-26 14:21:14,516 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-07-26 14:21:14,518 ] 25 root - INFO - Processing RAG query: What is the current weather in Kolkata
[ 2025-07-26 14:21:14,518 ] 62 root - INFO - \U0001f680 Starting 'run'...
[ 2025-07-26 14:21:14,521 ] 62 root - INFO - \U0001f680 Starting 'route_question'...
[ 2025-07-26 14:21:14,524 ] 480 groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-145ee3f5-453d-4a76-9d1d-9e2abf06944d', 'json_data': {'messages': [{'role': 'system', 'content': 'You are an expert assistant that uses available tool `web_search` if you are uncertain.\n            Always answer if you know; otherwise say "I don\'t know." \n            If you say that, immediately trigger a web_search tool call.'}, {'role': 'user', 'content': 'What is the current weather in Kolkata'}], 'model': 'llama3-8b-8192', 'n': 1, 'reasoning_effort': None, 'reasoning_format': None, 'service_tier': 'on_demand', 'stop': None, 'stream': False, 'temperature': 0.7}}
[ 2025-07-26 14:21:14,527 ] 966 groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
[ 2025-07-26 14:21:15,847 ] 1004 groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Sat, 26 Jul 2025 08:51:15 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'bom', 'x-ratelimit-limit-requests': '50000', 'x-ratelimit-limit-tokens': '100000', 'x-ratelimit-remaining-requests': '49999', 'x-ratelimit-remaining-tokens': '99923', 'x-ratelimit-reset-requests': '1.728s', 'x-ratelimit-reset-tokens': '46.2ms', 'x-request-id': 'req_01k12yvsfje0zr3sapxas5hcs5', 'via': '1.1 google', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9652a776181a3b34-BOM', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
[ 2025-07-26 14:21:15,851 ] 61 root - INFO -  ================= routing completed =================
[ 2025-07-26 14:21:15,852 ] 68 root - INFO - \u2705 Finished 'route_question' in 1.3309 seconds.
[ 2025-07-26 14:21:15,853 ] 62 root - INFO - \U0001f680 Starting 'web_search'...
[ 2025-07-26 14:21:15,854 ] 54 root - INFO - =========================== TaivilySearchTool Invoke ===========================
[ 2025-07-26 14:21:15,856 ] 59 root - INFO - Web Search Started
[ 2025-07-26 14:21:16,769 ] 48 root - ERROR - Exception raise: 401 Client Error: Unauthorized for url: https://api.tavily.com/search
[ 2025-07-26 14:21:16,770 ] 74 root - ERROR - Exception raise in web search: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]
[ 2025-07-26 14:21:16,770 ] 135 root - INFO - Agent Weorflow Error: Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [63] error message [Error occurred python script name [D:\callagent-timeout\dev-calling-agent\src\agent\nodes\web_search.py] line number [35] error message [401 Client Error: Unauthorized for url: https://api.tavily.com/search]]
[ 2025-07-26 14:21:16,770 ] 68 root - INFO - \u2705 Finished 'run' in 2.2524 seconds.
[ 2025-07-26 14:21:16,771 ] 40 root - ERROR - RAG query tool failed: 'NoneType' object has no attribute 'get'
[ 2025-07-26 14:21:16,772 ] 19 livekit.agents - WARNING - Running <Task finished name='Task-327' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 2.25 seconds
[ 2025-07-26 14:21:16,775 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-07-26 14:21:59,986 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-07-26 14:21:59,990 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-07-26 14:21:59,999 ] 650 livekit.agents - DEBUG - session closed
[ 2025-07-26 14:22:00,000 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-07-26 14:22:00,002 ] 278 livekit.agents - DEBUG - job exiting
